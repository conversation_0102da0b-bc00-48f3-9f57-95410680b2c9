import { Metadata } from "next";
import { notFound, redirect } from "next/navigation";

interface SearchPageProps {
  params: {
    query: string;
  };
  searchParams: {
    page?: string;
  };
}

// Популярные поисковые запросы для предварительной генерации
const POPULAR_QUERIES = [
  "цемент",
  "арматура", 
  "кирпич",
  "плитка",
  "инструменты",
  "краска",
  "утеплитель",
  "гипсокартон",
  "двери",
  "окна",
  "кровля",
  "сантехника",
  "электрика",
  "напольные покрытия",
  "обои",
];

// Генерация статических путей для популярных запросов
export async function generateStaticPaths() {
  const paths = POPULAR_QUERIES.map((query) => ({
    params: { query: encodeURIComponent(query) },
  }));

  return {
    paths,
    fallback: "blocking", // ISR для остальных запросов
  };
}

// Генерация метаданных для SEO
export async function generateMetadata({
  params,
  searchParams,
}: SearchPageProps): Promise<Metadata> {
  const query = decodeURIComponent(params.query);
  const page = parseInt(searchParams.page || "1");

  const title = page > 1 
    ? `Поиск: ${query} - Страница ${page} | SADI.KZ`
    : `Поиск: ${query} | SADI.KZ`;

  const description = `Результаты поиска по запросу "${query}". Строительные материалы и оборудование от проверенных поставщиков в SADI.KZ.`;

  return {
    title,
    description: description.slice(0, 160),
    keywords: [
      query,
      "поиск",
      "строительные материалы",
      "SADI.KZ",
      "купить",
      "поставщики",
      "цены",
    ].join(", "),
    openGraph: {
      title,
      description: description.slice(0, 160),
      type: "website",
      siteName: "SADI.KZ",
    },
    alternates: {
      canonical: `https://sadi.kz/search/${params.query}`,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function SearchPage({ 
  params, 
  searchParams 
}: SearchPageProps) {
  const query = decodeURIComponent(params.query);
  const page = parseInt(searchParams.page || "1");

  // Валидация поискового запроса
  if (!query || query.trim().length < 2) {
    notFound();
  }

  // Редирект на страницу каталога с поисковым запросом
  const redirectUrl = page > 1 
    ? `/products/page/${page}?search=${encodeURIComponent(query)}`
    : `/products/page/1?search=${encodeURIComponent(query)}`;

  redirect(redirectUrl);
}

// ISR - обновление каждые 12 часов
export const revalidate = 43200;
