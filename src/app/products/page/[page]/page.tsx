import { Metadata } from "next";
import { notFound } from "next/navigation";
import PriceListClient from "@/components/PriceListClient";
import ApiService from "@/services/api.service";

interface ProductsPageProps {
  params: {
    page: string;
  };
}

// Генерируем статические параметры для страниц каталога
export async function generateStaticParams() {
  try {
    // Определяем количество страниц для прегенерации
    // Можно увеличить это число для большего SEO покрытия
    const maxPages = 50; // Прегенерируем первые 50 страниц
    
    const pages = Array.from({ length: maxPages }, (_, i) => ({
      page: (i + 1).toString(),
    }));

    console.log(`Прегенерируем ${pages.length} страниц каталога`);
    return pages;
  } catch (error) {
    console.error("Ошибка при генерации статических параметров страниц:", error);
    return [{ page: "1" }]; // Минимум первая страница
  }
}

// Генерируем метаданные для SEO
export async function generateMetadata({
  params,
}: ProductsPageProps): Promise<Metadata> {
  const pageNumber = parseInt(params.page);
  
  if (isNaN(pageNumber) || pageNumber < 1) {
    return {
      title: "Страница не найдена | SADI.KZ",
      description: "Запрашиваемая страница каталога не найдена",
    };
  }

  try {
    // Получаем данные для текущей страницы
    const response = await ApiService.getProductsWithPagination(pageNumber, 20);
    const productsCount = response.data?.length || 0;
    const totalItems = response.pagination?.totalItems || 0;

    const title = pageNumber === 1 
      ? "Строительные материалы и оборудование | SADI.KZ"
      : `Строительные материалы - страница ${pageNumber} | SADI.KZ`;

    const description = pageNumber === 1
      ? "Каталог строительных материалов, инструментов и оборудования. Лучшие цены от проверенных поставщиков в Казахстане."
      : `Каталог строительных материалов - страница ${pageNumber}. Найдено ${totalItems} товаров от проверенных поставщиков.`;

    // Генерируем ключевые слова на основе товаров на странице
    const keywords = [
      "строительные материалы",
      "инструменты",
      "оборудование", 
      "купить в Казахстане",
      "SADI.KZ",
      "поставщики",
      "оптовые цены"
    ];

    // Добавляем названия товаров в ключевые слова (первые 5)
    if (response.data && response.data.length > 0) {
      const productNames = response.data
        .slice(0, 5)
        .map((product: any) => product.MaterialName || product.Name || product.name)
        .filter(Boolean);
      keywords.push(...productNames);
    }

    return {
      title,
      description,
      keywords: keywords.join(", "),
      openGraph: {
        title,
        description,
        type: "website",
        url: `/products/page/${pageNumber}`,
      },
      twitter: {
        card: "summary",
        title,
        description,
      },
      alternates: {
        canonical: `/products/page/${pageNumber}`,
        ...(pageNumber > 1 && {
          prev: `/products/page/${pageNumber - 1}`,
        }),
        ...(response.pagination?.hasNextPage && {
          next: `/products/page/${pageNumber + 1}`,
        }),
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
        },
      },
    };
  } catch (error) {
    console.error("Ошибка при генерации метаданных страницы:", error);
    return {
      title: `Каталог товаров - страница ${pageNumber} | SADI.KZ`,
      description: "Строительные материалы и оборудование в SADI.KZ",
    };
  }
}

export default async function ProductsPage({ params }: ProductsPageProps) {
  const pageNumber = parseInt(params.page);
  
  if (isNaN(pageNumber) || pageNumber < 1) {
    notFound();
  }

  // Проверяем, существует ли страница (есть ли на ней товары)
  try {
    const response = await ApiService.getProductsWithPagination(pageNumber, 20);
    
    if (!response.data || response.data.length === 0) {
      // Если это не первая страница и нет товаров, показываем 404
      if (pageNumber > 1) {
        notFound();
      }
    }

    // Передаем начальную страницу в клиентский компонент
    return <PriceListClient initialPage={pageNumber} />;
  } catch (error) {
    console.error(`Ошибка при загрузке страницы ${pageNumber}:`, error);
    
    // Если это первая страница, показываем компонент (он сам обработает ошибку)
    if (pageNumber === 1) {
      return <PriceListClient initialPage={1} />;
    }
    
    // Для остальных страниц показываем 404
    notFound();
  }
}
