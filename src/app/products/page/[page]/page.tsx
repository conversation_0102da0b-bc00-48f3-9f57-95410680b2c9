import { Metadata } from "next";
import { notFound } from "next/navigation";
import ApiService from "@/services/api.service";
import ProductsPageClient from "@/components/ProductsPageClient";

interface ProductsPageProps {
  params: {
    page: string;
  };
  searchParams: {
    category?: string;
    search?: string;
  };
}

const PRODUCTS_PER_PAGE = 20;
const MAX_PREGENERATED_PAGES = 10;

// Генерация статических путей для первых страниц
export async function generateStaticPaths() {
  const paths = [];
  
  // Генерируем первые 10 страниц
  for (let i = 1; i <= MAX_PREGENERATED_PAGES; i++) {
    paths.push({
      params: { page: i.toString() },
    });
  }

  return {
    paths,
    fallback: "blocking", // ISR для остальных страниц
  };
}

// Генерация метаданных для SEO
export async function generateMetadata({
  params,
  searchParams,
}: ProductsPageProps): Promise<Metadata> {
  const pageNum = parseInt(params.page) || 1;
  const { category, search } = searchParams;

  let title = `Строительные материалы`;
  let description = `Каталог строительных материалов и оборудования`;

  if (search) {
    title = `Поиск: ${search}`;
    description = `Результаты поиска по запросу "${search}" - строительные материалы и оборудование`;
  } else if (category) {
    title = `Категория: ${category}`;
    description = `Строительные материалы в категории "${category}"`;
  }

  if (pageNum > 1) {
    title += ` - Страница ${pageNum}`;
    description += ` - Страница ${pageNum}`;
  }

  title += ` | SADI.KZ`;

  const canonical = `https://sadi.kz/products/page/${pageNum}`;
  const prevPage = pageNum > 1 ? `https://sadi.kz/products/page/${pageNum - 1}` : null;
  const nextPage = `https://sadi.kz/products/page/${pageNum + 1}`;

  return {
    title,
    description: description.slice(0, 160),
    keywords: [
      "строительные материалы",
      "каталог",
      "купить",
      "SADI.KZ",
      "поставщики",
      "оптом",
      "розница",
      search,
      category,
    ].filter(Boolean).join(", "),
    openGraph: {
      title,
      description: description.slice(0, 160),
      type: "website",
      siteName: "SADI.KZ",
    },
    alternates: {
      canonical,
      ...(prevPage && { prev: prevPage }),
      next: nextPage,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function ProductsPage({ 
  params, 
  searchParams 
}: ProductsPageProps) {
  const pageNum = parseInt(params.page) || 1;
  const { category, search } = searchParams;

  if (pageNum < 1) {
    notFound();
  }

  try {
    // Получаем данные товаров
    let response;
    
    if (search && category) {
      response = await ApiService.getProductsWithPagination(
        pageNum,
        PRODUCTS_PER_PAGE,
        category,
        search
      );
    } else if (search) {
      response = await ApiService.searchProductsWithPagination(
        search,
        pageNum,
        PRODUCTS_PER_PAGE
      );
    } else if (category) {
      response = await ApiService.getProductsWithPagination(
        pageNum,
        PRODUCTS_PER_PAGE,
        category
      );
    } else {
      response = await ApiService.getProductsWithPagination(
        pageNum,
        PRODUCTS_PER_PAGE
      );
    }

    if (!response.data || response.data.length === 0) {
      if (pageNum === 1) {
        // Первая страница без товаров - показываем пустой каталог
        response = {
          data: [],
          pagination: {
            currentPage: 1,
            totalPages: 1,
            totalItems: 0,
            pageSize: PRODUCTS_PER_PAGE,
            hasNextPage: false,
            hasPreviousPage: false,
          }
        };
      } else {
        // Страница больше 1 без товаров - 404
        notFound();
      }
    }

    // JSON-LD Schema.org разметка для каталога
    const jsonLd = {
      "@context": "https://schema.org",
      "@type": "CollectionPage",
      name: search ? `Поиск: ${search}` : category ? `Категория: ${category}` : "Каталог строительных материалов",
      description: `Страница ${pageNum} каталога строительных материалов SADI.KZ`,
      url: `https://sadi.kz/products/page/${pageNum}`,
      mainEntity: {
        "@type": "ItemList",
        numberOfItems: response.data.length,
        itemListElement: response.data.map((product: any, index: number) => ({
          "@type": "ListItem",
          position: (pageNum - 1) * PRODUCTS_PER_PAGE + index + 1,
          item: {
            "@type": "Product",
            name: product.MaterialName || product.name,
            url: `https://sadi.kz/products/${product.MaterialCode || product.code || product.id}`,
            image: product.imageUrl || "/images/placeholder.png",
            offers: {
              "@type": "Offer",
              priceCurrency: "KZT",
              price: product.RetailPrice || product.WholesalePrice || 0,
              availability: "https://schema.org/InStock",
            },
          },
        })),
      },
    };

    return (
      <>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
        <ProductsPageClient 
          products={response.data}
          pagination={response.pagination}
          currentPage={pageNum}
          category={category}
          searchQuery={search}
        />
      </>
    );
  } catch (error) {
    console.error("Ошибка при загрузке каталога:", error);
    notFound();
  }
}

// ISR - обновление каждые 6 часов
export const revalidate = 21600;
