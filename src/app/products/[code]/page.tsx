import { Metadata } from "next";
import { notFound } from "next/navigation";
import ApiService from "@/services/api.service";
import ProductDetailClient from "@/components/ProductDetailClient";

interface ProductPageProps {
  params: {
    code: string;
  };
}

// Генерируем статические параметры для популярных товаров
export async function generateStaticParams() {
  try {
    // Получаем первые несколько страниц товаров для прегенерации
    const pages = [1, 2, 3, 4, 5]; // Можно увеличить для большего покрытия
    const allProducts: any[] = [];

    for (const page of pages) {
      try {
        const response = await ApiService.getProductsWithPagination(page, 20);
        if (response.data && response.data.length > 0) {
          allProducts.push(...response.data);
        }
      } catch (error) {
        console.error(`Ошибка при получении страницы ${page}:`, error);
      }
    }

    // Извлекаем коды товаров
    const productCodes = allProducts
      .map((product) => {
        // Пробуем разные поля для кода товара
        const code = product.MaterialCode || 
                    product.Code || 
                    product.code || 
                    product.id || 
                    product.MaterialId;
        return code ? { code: code.toString() } : null;
      })
      .filter(Boolean)
      .slice(0, 100); // Ограничиваем количество для начала

    console.log(`Прегенерируем ${productCodes.length} страниц товаров`);
    return productCodes;
  } catch (error) {
    console.error("Ошибка при генерации статических параметров:", error);
    return [];
  }
}

// Генерируем метаданные для SEO
export async function generateMetadata({
  params,
}: ProductPageProps): Promise<Metadata> {
  try {
    // Ищем товар по коду
    const product = await findProductByCode(params.code);

    if (!product) {
      return {
        title: "Товар не найден | SADI.KZ",
        description: "Запрашиваемый товар не найден в каталоге SADI.KZ",
      };
    }

    const productName = product.MaterialName || product.Name || product.name || "Товар";
    const description = product.Description || 
                       product.description || 
                       `${productName} - купить в SADI.KZ. Лучшие цены от проверенных поставщиков.`;

    // Формируем цену для мета-описания
    const priceText = product.RetailPrice 
      ? `от ${new Intl.NumberFormat("ru-RU").format(product.RetailPrice)} ₸`
      : "по запросу";

    return {
      title: `${productName} - ${priceText} | SADI.KZ`,
      description: `${description} ${priceText}. Быстрая доставка по Казахстану.`,
      keywords: [
        productName,
        product.MaterialCode || product.Code || params.code,
        "строительные материалы",
        "купить в Казахстане",
        "SADI.KZ"
      ].join(", "),
      openGraph: {
        title: `${productName} - ${priceText}`,
        description: `${description} ${priceText}. Купить в SADI.KZ`,
        type: "website",
        images: product.imageUrl ? [product.imageUrl] : [],
      },
      twitter: {
        card: "summary_large_image",
        title: `${productName} - ${priceText}`,
        description: description,
      },
      alternates: {
        canonical: `/products/${params.code}`,
      },
    };
  } catch (error) {
    console.error("Ошибка при генерации метаданных:", error);
    return {
      title: "Товар | SADI.KZ",
      description: "Строительные материалы и оборудование в SADI.KZ",
    };
  }
}

// Функция поиска товара по коду
async function findProductByCode(code: string) {
  try {
    // Сначала пробуем найти через прямой поиск по коду
    const searchResults = await ApiService.getProductsWithPagination(1, 50, code);
    
    if (searchResults.data && searchResults.data.length > 0) {
      // Ищем точное совпадение по коду
      const exactMatch = searchResults.data.find((product: any) => {
        const productCode = product.MaterialCode || 
                           product.Code || 
                           product.code || 
                           product.id || 
                           product.MaterialId;
        return productCode && productCode.toString() === code;
      });
      
      if (exactMatch) return exactMatch;
      
      // Если точного совпадения нет, возвращаем первый результат
      return searchResults.data[0];
    }

    // Если не найден через фильтр, пробуем поиск по названию
    const nameSearchResults = await ApiService.searchProductsWithPagination(code, 1, 20);
    if (nameSearchResults.data && nameSearchResults.data.length > 0) {
      return nameSearchResults.data[0];
    }

    return null;
  } catch (error) {
    console.error(`Ошибка при поиске товара по коду ${code}:`, error);
    return null;
  }
}

export default async function ProductCodePage({ params }: ProductPageProps) {
  const product = await findProductByCode(params.code);

  if (!product) {
    notFound();
  }

  // Преобразуем продукт в формат, ожидаемый компонентом
  const productId = product.id || product.MaterialId || params.code;

  return <ProductDetailClient productId={productId} />;
}
