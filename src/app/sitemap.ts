import { MetadataRoute } from "next";
import ApiService from "@/services/api.service";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = "https://sadi.kz"; // Замените на ваш домен

  try {
    // Статические страницы
    const staticPages: MetadataRoute.Sitemap = [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: "daily",
        priority: 1,
      },
      {
        url: `${baseUrl}/auth`,
        lastModified: new Date(),
        changeFrequency: "monthly",
        priority: 0.5,
      },
      {
        url: `${baseUrl}/cart`,
        lastModified: new Date(),
        changeFrequency: "weekly",
        priority: 0.6,
      },
    ];

    // Генерируем страницы каталога
    const catalogPages: MetadataRoute.Sitemap = [];
    const maxCatalogPages = 50; // Количество страниц каталога для sitemap

    for (let page = 1; page <= maxCatalogPages; page++) {
      catalogPages.push({
        url: `${baseUrl}/products/page/${page}`,
        lastModified: new Date(),
        changeFrequency: "daily",
        priority: page === 1 ? 0.9 : 0.7,
      });
    }

    // Генерируем страницы товаров
    const productPages: MetadataRoute.Sitemap = [];
    const maxProductPages = 10; // Количество страниц для получения товаров

    for (let page = 1; page <= maxProductPages; page++) {
      try {
        const response = await ApiService.getProductsWithPagination(page, 20);
        
        if (response.data && response.data.length > 0) {
          response.data.forEach((product: any) => {
            const productCode = product.MaterialCode || 
                               product.Code || 
                               product.code || 
                               product.id || 
                               product.MaterialId;
            
            if (productCode) {
              productPages.push({
                url: `${baseUrl}/products/${productCode}`,
                lastModified: new Date(),
                changeFrequency: "weekly",
                priority: 0.8,
              });
            }
          });
        }
      } catch (error) {
        console.error(`Ошибка при получении товаров для sitemap, страница ${page}:`, error);
      }
    }

    console.log(`Sitemap сгенерирован: ${staticPages.length} статических страниц, ${catalogPages.length} страниц каталога, ${productPages.length} страниц товаров`);

    return [...staticPages, ...catalogPages, ...productPages];
  } catch (error) {
    console.error("Ошибка при генерации sitemap:", error);
    
    // Возвращаем минимальный sitemap в случае ошибки
    return [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: "daily",
        priority: 1,
      },
    ];
  }
}
