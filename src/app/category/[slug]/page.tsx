import { <PERSON>adata } from "next";
import { notFound, redirect } from "next/navigation";
import ApiService from "@/services/api.service";

interface CategoryPageProps {
  params: {
    slug: string;
  };
  searchParams: {
    page?: string;
  };
}

// Популярные категории для предварительной генерации
const POPULAR_CATEGORIES = [
  { slug: "cement", code: "24-030101", name: "Цемент" },
  { slug: "armatura", code: "24-030201", name: "Арматура" },
  { slug: "kirpich", code: "24-020101", name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" },
  { slug: "plitka", code: "24-040101", name: "Плит<PERSON>а" },
  { slug: "instrumenty", code: "25-010101", name: "Инструменты" },
  { slug: "obor<PERSON><PERSON><PERSON>", code: "25-020101", name: "Оборудование" },
  { slug: "krovlya", code: "24-050101", name: "Кровельные материалы" },
  { slug: "uteplitel", code: "24-060101", name: "Утеплители" },
  { slug: "gipsokarton", code: "24-070101", name: "Гипсокартон" },
  { slug: "dveri", code: "24-080101", name: "Двери" },
];

// Генерация статических путей для популярных категорий
export async function generateStaticPaths() {
  const paths = POPULAR_CATEGORIES.map((category) => ({
    params: { slug: category.slug },
  }));

  return {
    paths,
    fallback: "blocking", // ISR для остальных категорий
  };
}

// Функция для получения информации о категории по slug
function getCategoryBySlug(slug: string) {
  return POPULAR_CATEGORIES.find(cat => cat.slug === slug);
}

// Генерация метаданных для SEO
export async function generateMetadata({
  params,
  searchParams,
}: CategoryPageProps): Promise<Metadata> {
  const category = getCategoryBySlug(params.slug);
  const page = parseInt(searchParams.page || "1");

  if (!category) {
    return {
      title: "Категория не найдена | SADI.KZ",
      description: "Запрашиваемая категория не найдена в каталоге SADI.KZ",
    };
  }

  const title = page > 1 
    ? `${category.name} - Страница ${page} | SADI.KZ`
    : `${category.name} - Купить в SADI.KZ`;

  const description = `${category.name} - широкий выбор строительных материалов от проверенных поставщиков. Лучшие цены, быстрая доставка по Казахстану.`;

  return {
    title,
    description: description.slice(0, 160),
    keywords: [
      category.name.toLowerCase(),
      "купить",
      "строительные материалы",
      "SADI.KZ",
      "поставщики",
      "цены",
      "Казахстан",
    ].join(", "),
    openGraph: {
      title,
      description: description.slice(0, 160),
      type: "website",
      siteName: "SADI.KZ",
    },
    alternates: {
      canonical: `https://sadi.kz/category/${params.slug}`,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function CategoryPage({ 
  params, 
  searchParams 
}: CategoryPageProps) {
  const category = getCategoryBySlug(params.slug);
  const page = parseInt(searchParams.page || "1");

  if (!category) {
    notFound();
  }

  // Редирект на страницу каталога с фильтром по категории
  const redirectUrl = page > 1 
    ? `/products/page/${page}?category=${category.code}`
    : `/products/page/1?category=${category.code}`;

  redirect(redirectUrl);
}

// ISR - обновление каждые 24 часа
export const revalidate = 86400;
