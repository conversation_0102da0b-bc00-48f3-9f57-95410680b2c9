"use client";

import React from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import Layout from "./Layout";
import ProductCard from "./ProductCard";
import Pagination from "./Pagination";

interface ProductsPageClientProps {
  products: any[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  currentPage: number;
  category?: string;
  searchQuery?: string;
}

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const Header = styled.div`
  margin-bottom: 30px;
`;

const Title = styled.h1`
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.3;
`;

const Description = styled.p`
  font-size: 16px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20px;
`;

const Breadcrumbs = styled.nav`
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;

  a {
    color: #0066cc;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  span {
    margin: 0 8px;
    color: #999;
  }
`;

const ProductGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 40px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
  }
`;

const NoResultsMessage = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
`;

const PaginationWrapper = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 40px;
`;

const SEOText = styled.div`
  margin-top: 40px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  
  h2 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
  }
  
  p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 10px;
  }
`;

const ProductsPageClient: React.FC<ProductsPageClientProps> = ({
  products,
  pagination,
  currentPage,
  category,
  searchQuery,
}) => {
  const router = useRouter();

  const handleProductClick = (productCode: string) => {
    router.push(`/products/${productCode}`);
  };

  const handlePageChange = (page: number) => {
    const url = new URL(window.location.href);
    url.pathname = `/products/page/${page}`;
    router.push(url.toString());
  };

  // Генерируем заголовок и описание
  const getTitle = () => {
    if (searchQuery) {
      return currentPage > 1 
        ? `Поиск: ${searchQuery} - Страница ${currentPage}`
        : `Поиск: ${searchQuery}`;
    }
    if (category) {
      return currentPage > 1 
        ? `Категория: ${category} - Страница ${currentPage}`
        : `Категория: ${category}`;
    }
    return currentPage > 1 
      ? `Каталог строительных материалов - Страница ${currentPage}`
      : "Каталог строительных материалов";
  };

  const getDescription = () => {
    if (searchQuery) {
      return `Найдено ${pagination.totalItems} товаров по запросу "${searchQuery}". Строительные материалы и оборудование от проверенных поставщиков.`;
    }
    if (category) {
      return `${pagination.totalItems} товаров в категории "${category}". Лучшие цены на строительные материалы от надежных поставщиков.`;
    }
    return `Каталог из ${pagination.totalItems} строительных материалов и оборудования. Сравните цены от разных поставщиков и выберите лучшее предложение.`;
  };

  const getBreadcrumbs = () => {
    const items = [
      { label: "Главная", href: "/" },
      { label: "Каталог", href: "/products/page/1" },
    ];

    if (category) {
      items.push({ label: category, href: "" });
    }
    if (searchQuery) {
      items.push({ label: `Поиск: ${searchQuery}`, href: "" });
    }
    if (currentPage > 1) {
      items.push({ label: `Страница ${currentPage}`, href: "" });
    }

    return items;
  };

  return (
    <Layout>
      <Container>
        <Header>
          <Breadcrumbs>
            {getBreadcrumbs().map((item, index) => (
              <React.Fragment key={index}>
                {index > 0 && <span>/</span>}
                {item.href ? (
                  <a href={item.href}>{item.label}</a>
                ) : (
                  <span>{item.label}</span>
                )}
              </React.Fragment>
            ))}
          </Breadcrumbs>

          <Title>{getTitle()}</Title>
          <Description>{getDescription()}</Description>
        </Header>

        {products.length > 0 ? (
          <>
            <ProductGrid>
              {products.map((product: any) => (
                <ProductCard
                  key={product.MaterialId || product.id}
                  product={product}
                  onClick={() =>
                    handleProductClick(
                      product.MaterialCode || product.code || product.id
                    )
                  }
                />
              ))}
            </ProductGrid>

            <PaginationWrapper>
              <Pagination
                currentPage={pagination.currentPage}
                totalPages={pagination.totalPages}
                totalItems={pagination.totalItems}
                pageSize={pagination.pageSize}
                onPageChange={handlePageChange}
              />
            </PaginationWrapper>
          </>
        ) : (
          <NoResultsMessage>
            {searchQuery || category
              ? "По вашему запросу ничего не найдено. Попробуйте изменить критерии поиска."
              : "Товары не найдены."}
          </NoResultsMessage>
        )}

        <SEOText>
          <h2>О каталоге строительных материалов SADI.KZ</h2>
          <p>
            SADI.KZ - это крупнейший маркетплейс строительных материалов в Казахстане. 
            У нас вы найдете более {pagination.totalItems} наименований товаров от проверенных поставщиков.
          </p>
          <p>
            Мы предлагаем широкий ассортимент: цемент, арматура, кирпич, плитка, 
            инструменты, оборудование и многое другое. Сравнивайте цены, выбирайте 
            лучшие предложения и покупайте напрямую у производителей и дистрибьюторов.
          </p>
          {currentPage > 1 && (
            <p>
              Вы просматриваете страницу {currentPage} из {pagination.totalPages}. 
              Всего в каталоге представлено {pagination.totalItems} товаров.
            </p>
          )}
        </SEOText>
      </Container>
    </Layout>
  );
};

export default ProductsPageClient;
