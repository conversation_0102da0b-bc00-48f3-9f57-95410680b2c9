"use client";

import React from "react";
import styled from "styled-components";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  onPageChange: (page: number) => void;
}

const PaginationContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  margin: 40px 0;
`;

const PaginationInfo = styled.div`
  font-size: 14px;
  color: #666;
  text-align: center;
`;

const PaginationControls = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
`;

const PageButton = styled.button<{ $active?: boolean; $disabled?: boolean }>`
  padding: 8px 12px;
  border: 1px solid #ddd;
  background-color: ${props => 
    props.$active ? '#0066cc' : 
    props.$disabled ? '#f5f5f5' : 'white'
  };
  color: ${props => 
    props.$active ? 'white' : 
    props.$disabled ? '#999' : '#333'
  };
  border-radius: 4px;
  cursor: ${props => props.$disabled ? 'not-allowed' : 'pointer'};
  font-size: 14px;
  min-width: 40px;
  transition: all 0.2s ease;

  &:hover {
    ${props => !props.$disabled && !props.$active && `
      background-color: #f0f0f0;
      border-color: #999;
    `}
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
`;

const Ellipsis = styled.span`
  padding: 8px 4px;
  color: #999;
  font-size: 14px;
`;

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  pageSize,
  onPageChange,
}) => {
  // Вычисляем диапазон отображаемых элементов
  const startItem = (currentPage - 1) * pageSize + 1;
  const endItem = Math.min(currentPage * pageSize, totalItems);

  // Функция для генерации номеров страниц для отображения
  const getPageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 7; // Максимальное количество видимых страниц

    if (totalPages <= maxVisiblePages) {
      // Если страниц мало, показываем все
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Если страниц много, показываем с многоточиями
      if (currentPage <= 4) {
        // Начало списка
        for (let i = 1; i <= 5; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 3) {
        // Конец списка
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - 4; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // Середина списка
        pages.push(1);
        pages.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const handlePageClick = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  if (totalPages <= 1) {
    return null; // Не показываем пагинацию, если страница одна или нет данных
  }

  return (
    <PaginationContainer>
      <PaginationInfo>
        Показано {startItem}-{endItem} из {totalItems} товаров
      </PaginationInfo>

      <PaginationControls>
        <PageButton
          $disabled={currentPage === 1}
          onClick={handlePrevious}
          disabled={currentPage === 1}
        >
          ← Назад
        </PageButton>

        {getPageNumbers().map((page, index) => {
          if (page === '...') {
            return <Ellipsis key={`ellipsis-${index}`}>...</Ellipsis>;
          }

          const pageNumber = page as number;
          return (
            <PageButton
              key={pageNumber}
              $active={pageNumber === currentPage}
              onClick={() => handlePageClick(pageNumber)}
            >
              {pageNumber}
            </PageButton>
          );
        })}

        <PageButton
          $disabled={currentPage === totalPages}
          onClick={handleNext}
          disabled={currentPage === totalPages}
        >
          Вперед →
        </PageButton>
      </PaginationControls>
    </PaginationContainer>
  );
};

export default Pagination;
