"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import Layout from "./Layout";

interface ProductDetailClientProps {
  product: any;
}

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const Breadcrumbs = styled.nav`
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;

  a {
    color: #0066cc;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  span {
    margin: 0 8px;
    color: #999;
  }
`;

const ProductContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
`;

const ImageSection = styled.div`
  display: flex;
  flex-direction: column;
`;

const MainImage = styled.img`
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  margin-bottom: 20px;
`;

const InfoSection = styled.div`
  display: flex;
  flex-direction: column;
`;

const Title = styled.h1`
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  line-height: 1.3;
`;

const PriceSection = styled.div`
  margin-bottom: 30px;
`;

const Price = styled.div`
  font-size: 24px;
  font-weight: bold;
  color: #0066cc;
  margin-bottom: 10px;
`;

const PriceLabel = styled.div`
  font-size: 14px;
  color: #666;
`;

const Description = styled.div`
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30px;
`;

const SpecsTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;

  th,
  td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
  }

  th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
  }

  td {
    color: #666;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 30px;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const AddToCartButton = styled.button`
  background-color: #0066cc;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  flex: 1;

  &:hover {
    background-color: #0052a3;
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const ContactButton = styled.button`
  background-color: transparent;
  color: #0066cc;
  border: 2px solid #0066cc;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;

  &:hover {
    background-color: #0066cc;
    color: white;
  }
`;

const BackButton = styled.button`
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 20px;

  &:hover {
    background-color: #f5f5f5;
    border-color: #999;
  }
`;

const ProductDetailClient: React.FC<ProductDetailClientProps> = ({
  product,
}) => {
  const router = useRouter();
  const [quantity, setQuantity] = useState(1);

  const handleAddToCart = () => {
    // Логика добавления в корзину
    console.log("Добавлено в корзину:", product, "количество:", quantity);
    router.push("/cart");
  };

  const handleContact = () => {
    // Логика связи с поставщиком
    console.log("Связаться с поставщиком для товара:", product);
  };

  const handleBack = () => {
    router.back();
  };

  const productName = product.MaterialName || product.name || "Товар";
  const productCode = product.MaterialCode || product.code || product.id;
  const retailPrice = product.RetailPrice;
  const wholesalePrice = product.WholesalePrice;
  const description = product.Description || `${productName} - качественные строительные материалы от проверенных поставщиков.`;
  const imageUrl = product.imageUrl || "/images/placeholder.png";

  // Характеристики товара
  const specs = [
    { label: "Код товара", value: productCode },
    { label: "Единица измерения", value: product.Unit || "шт" },
    { label: "Категория", value: product.CategoryName || "Строительные материалы" },
    ...(product.Brand ? [{ label: "Бренд", value: product.Brand }] : []),
    ...(product.Country ? [{ label: "Страна производства", value: product.Country }] : []),
    ...(product.Weight ? [{ label: "Вес", value: `${product.Weight} кг` }] : []),
    ...(product.Dimensions ? [{ label: "Размеры", value: product.Dimensions }] : []),
  ];

  return (
    <Layout>
      <Container>
        <BackButton onClick={handleBack}>
          ← Назад к каталогу
        </BackButton>

        <Breadcrumbs>
          <a href="/products/page/1">Каталог</a>
          <span>/</span>
          <span>{productName}</span>
        </Breadcrumbs>

        <ProductContainer>
          <ImageSection>
            <MainImage src={imageUrl} alt={productName} />
          </ImageSection>

          <InfoSection>
            <Title>{productName}</Title>

            <PriceSection>
              {retailPrice && (
                <>
                  <Price>
                    {new Intl.NumberFormat("ru-RU").format(retailPrice)} ₸
                  </Price>
                  <PriceLabel>Розничная цена</PriceLabel>
                </>
              )}
              {wholesalePrice && wholesalePrice !== retailPrice && (
                <>
                  <Price style={{ fontSize: "18px", marginTop: "10px" }}>
                    {new Intl.NumberFormat("ru-RU").format(wholesalePrice)} ₸
                  </Price>
                  <PriceLabel>Оптовая цена</PriceLabel>
                </>
              )}
              {!retailPrice && !wholesalePrice && (
                <>
                  <Price>Цена по запросу</Price>
                  <PriceLabel>Свяжитесь с поставщиком</PriceLabel>
                </>
              )}
            </PriceSection>

            <Description>{description}</Description>

            <ActionButtons>
              <AddToCartButton onClick={handleAddToCart}>
                Добавить в корзину
              </AddToCartButton>
              <ContactButton onClick={handleContact}>
                Связаться с поставщиком
              </ContactButton>
            </ActionButtons>
          </InfoSection>
        </ProductContainer>

        <SpecsTable>
          <thead>
            <tr>
              <th>Характеристика</th>
              <th>Значение</th>
            </tr>
          </thead>
          <tbody>
            {specs.map((spec, index) => (
              <tr key={index}>
                <td>{spec.label}</td>
                <td>{spec.value}</td>
              </tr>
            ))}
          </tbody>
        </SpecsTable>
      </Container>
    </Layout>
  );
};

export default ProductDetailClient;
