# Настройка API

## Переключение между тестовым и основным API

В проекте реализован переключатель между тестовым и основным API. Для переключения измените значение `USE_PRODUCTION_API` в файле `src/config/api.js`.

### Тестовый API (по умолчанию)
```javascript
const USE_PRODUCTION_API = false;
```

- **URL**: `http://test.api.sadi.kz`
- **Изображения**: `https://test.sadi.kz`
- **Авторизация**: Не требуется
- **Протокол**: HTTP

### Основной API
```javascript
const USE_PRODUCTION_API = true;
```

- **URL**: `https://api.sadi.kz`
- **Изображения**: `https://sadi.kz`
- **Авторизация**: Требуется (Bearer Token)
- **Протокол**: HTTPS
- **Токен**: Автоматически запрашивается и обновляется
- **Срок жизни токена**: 14 дней

## Авторизация для основного API

При использовании основного API система автоматически:

1. **Запрашивает токен** при первом обращении к API
2. **Сохраняет токен** в localStorage
3. **Проверяет срок действия** токена перед каждым запросом
4. **Автоматически обновляет** токен при истечении
5. **Добавляет заголовок** `Authorization: Bearer <token>` ко всем запросам

### Параметры авторизации
- **URL токена**: `https://api.sadi.kz/Token`
- **Метод**: POST
- **Content-Type**: `application/x-www-form-urlencoded`
- **Параметры**:
  - `grant_type`: "password"
  - `username`: "<EMAIL>"
  - `password`: "310312"

## Отладка API

### Консоль разработчика
В консоли браузера доступны утилиты отладки:

```javascript
// Информация о текущей конфигурации API
window.apiDebug.getInfo()

// Проверка статуса авторизации
window.apiDebug.checkAuth()

// Очистка кэша
window.apiDebug.clearCache()

// Статистика кэша
window.apiDebug.getCacheStats()

// Тестовый запрос к API
window.apiDebug.testApi()

// Очистка токена
window.apiDebug.clearToken()

// Справка по командам
window.apiDebug.help()
```

### Компонент ApiStatus
Для визуального отображения статуса API можно использовать компонент `ApiStatus`:

```jsx
import ApiStatus from './components/ApiStatus';

// В компоненте
const [showApiStatus, setShowApiStatus] = useState(false);

return (
  <>
    {/* Ваш контент */}
    {showApiStatus && (
      <ApiStatus onClose={() => setShowApiStatus(false)} />
    )}
  </>
);
```

## Кэширование

Система автоматически кэширует:
- **Каталог товаров** (полная структура)
- **Список товаров** (с учетом пагинации и фильтров)
- **Фотографии товаров** (по ID товара)
- **Детали товаров** (по ID)

### Управление кэшем
```javascript
// Очистить весь кэш
ApiService.clearCache()

// Получить статистику кэша
ApiService.getCacheStats()
```

## Обработка ошибок

### Ошибки авторизации (401)
При получении ошибки 401 система автоматически:
1. Очищает сохраненный токен
2. При следующем запросе запрашивает новый токен

### Ошибки сети
Все ошибки сети логируются в консоль и передаются в компоненты для обработки.

## Переключение API во время разработки

### Способ 1: Изменение конфигурации
1. Откройте `src/config/api.js`
2. Измените `USE_PRODUCTION_API` на нужное значение
3. Перезагрузите страницу

### Способ 2: Через консоль (информационно)
```javascript
// Показать инструкции для переключения
window.apiDebug.switchToProduction() // для основного API
window.apiDebug.switchToTest()       // для тестового API
```

## Структура файлов

```
src/
├── config/
│   └── api.js              # Конфигурация API и переключатель
├── services/
│   ├── api.service.js      # Основной сервис для работы с API
│   └── auth.service.js     # Сервис авторизации и токенов
├── utils/
│   ├── apiDebug.js         # Утилиты отладки API
│   └── imageUtils.js       # Утилиты для работы с изображениями
└── components/
    └── ApiStatus.jsx       # Компонент отображения статуса API
```

## Примеры использования

### Получение данных с авторизацией
```javascript
import ApiService from './services/api.service';

// Система автоматически добавит токен авторизации если нужно
const products = await ApiService.getProducts();
const catalog = await ApiService.getCatalog();
```

### Проверка типа API
```javascript
import API_CONFIG from './config/api';

if (API_CONFIG.USE_PRODUCTION_API) {
  console.log('Используется основной API');
} else {
  console.log('Используется тестовый API');
}
```

### Получение информации об API
```javascript
const apiInfo = ApiService.getApiInfo();
console.log('Тип API:', apiInfo.apiType);
console.log('Требуется авторизация:', apiInfo.authRequired);
console.log('Статус авторизации:', apiInfo.authStatus);
```
