import API_CONFIG from "../config/api.js";

/**
 * Сервис для работы с авторизацией и токенами
 */
class AuthService {
  constructor() {
    this.tokenKey = "api_access_token";
    this.tokenExpiryKey = "api_token_expiry";
  }

  /**
   * Получить токен доступа
   * @returns {Promise<string|null>} - Токен доступа или null
   */
  async getAccessToken() {
    // Если авторизация не требуется, возвращаем null
    if (!API_CONFIG.AUTH_REQUIRED) {
      return null;
    }

    // Проверяем, есть ли действующий токен в localStorage
    const existingToken = this.getStoredToken();
    if (existingToken && this.isTokenValid()) {
      console.log("Используется существующий токен");
      return existingToken;
    }

    // Если токена нет или он истек, получаем новый
    console.log("Получение нового токена...");
    return await this.requestNewToken();
  }

  /**
   * Получить сохраненный токен из localStorage
   * @returns {string|null} - Токен или null
   */
  getStoredToken() {
    try {
      return localStorage.getItem(this.tokenKey);
    } catch (error) {
      console.error("Ошибка при получении токена из localStorage:", error);
      return null;
    }
  }

  /**
   * Проверить, действителен ли токен
   * @returns {boolean} - true, если токен действителен
   */
  isTokenValid() {
    try {
      const expiryTime = localStorage.getItem(this.tokenExpiryKey);
      if (!expiryTime) {
        return false;
      }

      const expiryDate = new Date(expiryTime);
      const now = new Date();
      
      return expiryDate > now;
    } catch (error) {
      console.error("Ошибка при проверке срока действия токена:", error);
      return false;
    }
  }

  /**
   * Запросить новый токен с сервера
   * @returns {Promise<string|null>} - Новый токен или null при ошибке
   */
  async requestNewToken() {
    try {
      if (!API_CONFIG.TOKEN_URL || !API_CONFIG.AUTH_CREDENTIALS) {
        throw new Error("Не настроены параметры авторизации");
      }

      // Подготавливаем данные для запроса токена
      const formData = new URLSearchParams();
      Object.entries(API_CONFIG.AUTH_CREDENTIALS).forEach(([key, value]) => {
        formData.append(key, value);
      });

      console.log(`Запрос токена: ${API_CONFIG.TOKEN_URL}`);

      const response = await fetch(API_CONFIG.TOKEN_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (!data.access_token) {
        throw new Error("Токен не получен от сервера");
      }

      // Сохраняем токен и время истечения
      this.saveToken(data.access_token, data.expires_in);

      console.log("Токен успешно получен и сохранен");
      return data.access_token;
    } catch (error) {
      console.error("Ошибка при получении токена:", error);
      this.clearToken();
      return null;
    }
  }

  /**
   * Сохранить токен в localStorage
   * @param {string} token - Токен доступа
   * @param {number} expiresIn - Время жизни токена в секундах
   */
  saveToken(token, expiresIn) {
    try {
      localStorage.setItem(this.tokenKey, token);

      // Вычисляем время истечения токена
      const expiryTime = new Date();
      if (expiresIn) {
        // Если сервер предоставил время жизни в секундах
        expiryTime.setSeconds(expiryTime.getSeconds() + expiresIn);
      } else {
        // Используем настройку из конфигурации (14 дней)
        expiryTime.setDate(expiryTime.getDate() + API_CONFIG.TOKEN_LIFETIME_DAYS);
      }

      localStorage.setItem(this.tokenExpiryKey, expiryTime.toISOString());
    } catch (error) {
      console.error("Ошибка при сохранении токена:", error);
    }
  }

  /**
   * Очистить сохраненный токен
   */
  clearToken() {
    try {
      localStorage.removeItem(this.tokenKey);
      localStorage.removeItem(this.tokenExpiryKey);
      console.log("Токен очищен");
    } catch (error) {
      console.error("Ошибка при очистке токена:", error);
    }
  }

  /**
   * Получить заголовки авторизации для запросов
   * @returns {Promise<Object>} - Объект с заголовками
   */
  async getAuthHeaders() {
    if (!API_CONFIG.AUTH_REQUIRED) {
      return {};
    }

    const token = await this.getAccessToken();
    if (!token) {
      console.warn("Не удалось получить токен авторизации");
      return {};
    }

    return {
      Authorization: `Bearer ${token}`,
    };
  }

  /**
   * Проверить, требуется ли авторизация
   * @returns {boolean} - true, если требуется авторизация
   */
  isAuthRequired() {
    return API_CONFIG.AUTH_REQUIRED;
  }

  /**
   * Получить информацию о текущем состоянии авторизации
   * @returns {Object} - Информация о состоянии авторизации
   */
  getAuthStatus() {
    if (!API_CONFIG.AUTH_REQUIRED) {
      return {
        required: false,
        authenticated: true,
        message: "Авторизация не требуется (тестовый API)",
      };
    }

    const token = this.getStoredToken();
    const isValid = this.isTokenValid();

    return {
      required: true,
      authenticated: token && isValid,
      hasToken: !!token,
      tokenValid: isValid,
      message: token && isValid 
        ? "Авторизован" 
        : token 
          ? "Токен истек" 
          : "Токен отсутствует",
    };
  }
}

// Создаем единственный экземпляр сервиса
const authService = new AuthService();

export default authService;
