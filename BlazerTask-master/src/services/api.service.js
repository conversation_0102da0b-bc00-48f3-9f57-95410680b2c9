import API_CONFIG from "../config/api.js";
import authService from "./auth.service.js";

// Кэш для хранения данных
const cache = {
  catalog: null,
  products: null,
  paginatedProducts: {}, // Кэш для пагинированных данных
  photos: null, // Кэш для фотографий
  productPhotos: {}, // Кэш для фотографий конкретных продуктов
};

/**
 * Базовая функция для выполнения запросов к API с кэшированием и авторизацией
 * @param {string} url - URL для запроса
 * @param {Object} options - Опции запроса
 * @param {string} cacheKey - Ключ для кэширования результата
 * @returns {Promise<any>} - Результат запроса
 */
const fetchApi = async (url, options = {}, cacheKey = null) => {
  // Если указан ключ кэша и данные уже есть в кэше, возвращаем их
  if (cacheKey && cache[cacheKey]) {
    console.log(`Данные получены из кэша: ${cacheKey}`);
    return cache[cacheKey];
  }

  try {
    console.log(`Запрос к API: ${url}`);

    // Получаем заголовки авторизации
    const authHeaders = await authService.getAuthHeaders();

    // Объединяем заголовки
    const headers = {
      ...API_CONFIG.REQUEST_OPTIONS.headers,
      ...authHeaders,
      ...options.headers,
    };

    const response = await fetch(url, {
      ...API_CONFIG.REQUEST_OPTIONS,
      ...options,
      headers,
    });

    if (!response.ok) {
      // Если ошибка авторизации, очищаем токен
      if (response.status === 401 && authService.isAuthRequired()) {
        console.warn("Ошибка авторизации, очищаем токен");
        authService.clearToken();
      }
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();

    // Если указан ключ кэша, сохраняем данные в кэш
    if (cacheKey) {
      cache[cacheKey] = data;
      console.log(`Данные сохранены в кэш: ${cacheKey}`);
    }

    return data;
  } catch (error) {
    console.error("API request error:", error);
    throw error;
  }
};

/**
 * Сервис для работы с API
 */
const ApiService = {
  /**
   * Получить список всех продуктов
   * @returns {Promise<Array>} - Список продуктов
   */
  getProducts: async () => {
    // Если у нас уже есть кэшированные продукты, возвращаем их
    if (cache.products) {
      return cache.products;
    }

    try {
      // Получаем все продукты без фильтрации
      const products = await fetchApi(
        `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.PRODUCTS}`,
        {},
        "products"
      );

      return products;
    } catch (error) {
      console.error("Ошибка при получении продуктов:", error);
      throw error;
    }
  },

  /**
   * Получить список продуктов с пагинацией
   * @param {number} page - Номер страницы (начиная с 1)
   * @param {number} pageSize - Размер страницы
   * @param {Object} filters - Фильтры для запроса
   * @param {string} filters.code - Код категории
   * @param {string} filters.query - Поисковый запрос (передается как параметр name в API)
   * @returns {Promise<Object>} - Объект с данными пагинации и списком продуктов
   */
  getProductsWithPagination: async (page = 1, pageSize = 20, filters = {}) => {
    // Используем серверную пагинацию
    const { code, query } = filters;

    // Создаем ключ кэша с учетом всех параметров
    const filterKey = `${code || ""}_${query || ""}`;
    const paginationKey = `page_${page}_size_${pageSize}_filters_${filterKey}`;

    // Проверяем, есть ли данные в кэше
    if (cache.paginatedProducts[paginationKey]) {
      console.log(`Данные пагинации получены из кэша: ${paginationKey}`);
      return cache.paginatedProducts[paginationKey];
    }

    try {
      // Вычисляем offset для API (offset начинается с 0)
      const offset = (page - 1) * pageSize;

      // Формируем URL с параметрами пагинации и фильтрации
      const params = new URLSearchParams({
        offset: offset.toString(),
        limit: pageSize.toString(),
      });

      // Добавляем параметры фильтрации, если они есть
      if (code) {
        params.append("code", code);
      }
      if (query) {
        params.append("name", query);
      }

      const url = `${API_CONFIG.BASE_URL}${
        API_CONFIG.ENDPOINTS.PRODUCTS
      }/?${params.toString()}`;

      console.log(`Запрос к API с пагинацией и фильтрами: ${url}`);

      // Получаем заголовки авторизации
      const authHeaders = await authService.getAuthHeaders();

      // Объединяем заголовки
      const headers = {
        ...API_CONFIG.REQUEST_OPTIONS.headers,
        ...authHeaders,
      };

      // Делаем запрос к API
      const response = await fetch(url, {
        ...API_CONFIG.REQUEST_OPTIONS,
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // API возвращает просто массив продуктов
      let products, totalItems, totalPages;

      if (Array.isArray(data)) {
        products = data;

        // Поскольку API не предоставляет общее количество элементов,
        // используем эвристику для определения наличия следующих страниц
        if (data.length === pageSize) {
          // Если получили полную страницу, вероятно есть еще данные
          // Устанавливаем totalPages как минимум на текущую страницу + 1
          totalPages = page + 1;
          totalItems = page * pageSize + 1; // Приблизительная оценка
        } else {
          // Если получили неполную страницу, это последняя страница
          totalPages = page;
          totalItems = (page - 1) * pageSize + data.length;
        }
      } else if (data.results && Array.isArray(data.results)) {
        // Если API возвращает объект с полем results (стандарт Django REST)
        products = data.results;
        totalItems = data.count || data.total || products.length;
        totalPages = Math.ceil(totalItems / pageSize);
      } else if (data.data && Array.isArray(data.data)) {
        // Если API возвращает объект с полем data
        products = data.data;
        totalItems = data.count || data.total || products.length;
        totalPages = Math.ceil(totalItems / pageSize);
      } else {
        // Если структура неизвестна, пробуем использовать как есть
        products = data;
        totalItems = Array.isArray(data) ? data.length : 0;
        totalPages = 1;
      }

      // Формируем результат
      const result = {
        data: products,
        pagination: {
          page,
          pageSize,
          totalItems,
          totalPages,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      };

      // Сохраняем результат в кэш
      cache.paginatedProducts[paginationKey] = result;

      console.log(`Получено ${products.length} продуктов для страницы ${page}`);

      return result;
    } catch (error) {
      console.error("Ошибка при получении продуктов с пагинацией:", error);
      throw error;
    }
  },

  /**
   * Получить детальную информацию о продукте по ID
   * @param {number|string} id - ID продукта
   * @returns {Promise<Object>} - Информация о продукте
   */
  getProductById: async (id) => {
    // Проверяем, есть ли продукт в кэше всех продуктов
    if (cache.products) {
      const product = cache.products.find(
        (p) => p.MaterialId.toString() === id.toString()
      );
      if (product) {
        return product;
      }
    }

    const url = `${
      API_CONFIG.BASE_URL
    }${API_CONFIG.ENDPOINTS.PRODUCT_DETAILS.replace(":id", id)}`;
    return fetchApi(url);
  },

  /**
   * Получить структуру каталога
   * @returns {Promise<Object>} - Структура каталога
   */
  getCatalog: async () => {
    // Используем кэширование для каталога
    return fetchApi(
      `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CATALOG}`,
      {},
      "catalog"
    );
  },

  /**
   * Получить параметры фильтрации для API из объекта категории
   * @param {Object} category - Объект с параметрами категории
   * @returns {Object} - Объект с code для API
   * @private
   */
  _getCategoryApiParams: (category) => {
    if (!category) return {};

    // Используем самый глубокий уровень категории для фильтрации
    let code = null;

    if (category.group) {
      code = category.groupCode;
    } else if (category.subsection) {
      code = category.subsectionCode;
    } else if (category.section) {
      code = category.sectionCode;
    } else if (category.department) {
      code = category.departmentCode;
    }

    return { code };
  },

  /**
   * Фильтрация продуктов по категории с пагинацией (серверная)
   * @param {Object} category - Объект с параметрами категории
   * @param {number} page - Номер страницы (начиная с 1)
   * @param {number} pageSize - Размер страницы
   * @returns {Promise<Object>} - Объект с данными пагинации и отфильтрованным списком продуктов
   */
  filterProductsByCategoryWithPagination: async (
    category,
    page = 1,
    pageSize = 20
  ) => {
    // Получаем параметры фильтрации для API
    const categoryParams = ApiService._getCategoryApiParams(category);

    // Используем основной метод с фильтрацией
    return ApiService.getProductsWithPagination(page, pageSize, categoryParams);
  },

  /**
   * Поиск продуктов по запросу (серверный поиск без пагинации)
   * @param {string} query - Поисковый запрос
   * @returns {Promise<Array>} - Результаты поиска
   */
  searchProducts: async (query) => {
    if (!query) return [];

    try {
      // Формируем URL с параметром name для поиска
      const params = new URLSearchParams({
        name: query,
      });

      const url = `${API_CONFIG.BASE_URL}${
        API_CONFIG.ENDPOINTS.PRODUCTS
      }/?${params.toString()}`;

      console.log(`Запрос к API для поиска: ${url}`);

      // Получаем заголовки авторизации
      const authHeaders = await authService.getAuthHeaders();

      // Объединяем заголовки
      const headers = {
        ...API_CONFIG.REQUEST_OPTIONS.headers,
        ...authHeaders,
      };

      // Делаем запрос к API
      const response = await fetch(url, {
        ...API_CONFIG.REQUEST_OPTIONS,
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // API возвращает массив продуктов
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error("Ошибка при поиске продуктов:", error);
      throw error;
    }
  },

  /**
   * Поиск продуктов по запросу с пагинацией (серверный)
   * @param {string} query - Поисковый запрос
   * @param {number} page - Номер страницы (начиная с 1)
   * @param {number} pageSize - Размер страницы
   * @returns {Promise<Object>} - Объект с данными пагинации и результатами поиска
   */
  searchProductsWithPagination: async (query, page = 1, pageSize = 20) => {
    // Используем основной метод с поисковым запросом
    return ApiService.getProductsWithPagination(page, pageSize, { query });
  },

  /**
   * Поиск продуктов по запросу с фильтрацией по категории и пагинацией (серверный)
   * @param {string} query - Поисковый запрос
   * @param {Object} category - Объект с параметрами категории
   * @param {number} page - Номер страницы (начиная с 1)
   * @param {number} pageSize - Размер страницы
   * @returns {Promise<Object>} - Объект с данными пагинации и результатами поиска с фильтрацией
   */
  searchProductsWithCategoryAndPagination: async (
    query,
    category,
    page = 1,
    pageSize = 20
  ) => {
    // Получаем параметры фильтрации для API
    const categoryParams = ApiService._getCategoryApiParams(category);

    // Объединяем параметры категории и поискового запроса
    const filters = {
      ...categoryParams,
      query,
    };

    // Используем основной метод с комбинированными фильтрами
    return ApiService.getProductsWithPagination(page, pageSize, filters);
  },

  /**
   * Получить все фотографии товаров
   * @returns {Promise<Array>} - Список фотографий
   */
  getPhotos: async () => {
    // Если у нас уже есть кэшированные фотографии, возвращаем их
    if (cache.photos) {
      return cache.photos;
    }

    try {
      const photos = await fetchApi(
        `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.PHOTOS}`,
        {},
        "photos"
      );

      return photos;
    } catch (error) {
      console.error("Ошибка при получении фотографий:", error);
      return []; // Возвращаем пустой массив при ошибке
    }
  },

  /**
   * Получить фотографии для конкретного товара
   * @param {number|string} materialId - ID товара
   * @returns {Promise<Array>} - Список фотографий товара
   */
  getProductPhotos: async (materialId) => {
    const materialIdStr = materialId.toString();

    // Проверяем, есть ли фотографии для этого товара в кэше
    if (cache.productPhotos[materialIdStr]) {
      return cache.productPhotos[materialIdStr];
    }

    try {
      // Получаем все фотографии
      const allPhotos = await ApiService.getPhotos();

      // Фильтруем фотографии для конкретного товара
      const productPhotos = allPhotos.filter(
        (photo) => photo.MaterialId.toString() === materialIdStr
      );

      // Сохраняем в кэш
      cache.productPhotos[materialIdStr] = productPhotos;

      return productPhotos;
    } catch (error) {
      console.error(
        `Ошибка при получении фотографий для товара ${materialId}:`,
        error
      );
      return []; // Возвращаем пустой массив при ошибке
    }
  },

  /**
   * Получить первую фотографию товара (для карточки)
   * @param {number|string} materialId - ID товара
   * @returns {Promise<Object|null>} - Объект фотографии или null
   */
  getProductMainPhoto: async (materialId) => {
    try {
      const photos = await ApiService.getProductPhotos(materialId);
      return photos.length > 0 ? photos[0] : null;
    } catch (error) {
      console.error(
        `Ошибка при получении главной фотографии для товара ${materialId}:`,
        error
      );
      return null;
    }
  },

  /**
   * Получить информацию о текущей конфигурации API
   * @returns {Object} - Информация о конфигурации API
   */
  getApiInfo: () => {
    const authStatus = authService.getAuthStatus();

    return {
      apiType: API_CONFIG.USE_PRODUCTION_API ? "production" : "test",
      baseUrl: API_CONFIG.BASE_URL,
      imagesBaseUrl: API_CONFIG.IMAGES_BASE_URL,
      authRequired: API_CONFIG.AUTH_REQUIRED,
      authStatus,
      endpoints: API_CONFIG.ENDPOINTS,
    };
  },

  /**
   * Переключить тип API (только для разработки)
   * Внимание: этот метод изменяет глобальную конфигурацию
   * @param {boolean} useProduction - true для основного API, false для тестового
   */
  switchApiType: (useProduction) => {
    console.warn("Переключение типа API. Это действие очистит кэш и токены.");

    // Очищаем кэш
    Object.keys(cache).forEach((key) => {
      if (typeof cache[key] === "object" && cache[key] !== null) {
        if (Array.isArray(cache[key])) {
          cache[key] = [];
        } else {
          cache[key] = {};
        }
      } else {
        cache[key] = null;
      }
    });

    // Очищаем токены
    authService.clearToken();

    console.log(`API переключен на: ${useProduction ? "production" : "test"}`);
    console.log(
      "Для применения изменений необходимо обновить конфигурацию в api.js"
    );
  },

  /**
   * Очистить весь кэш
   */
  clearCache: () => {
    Object.keys(cache).forEach((key) => {
      if (typeof cache[key] === "object" && cache[key] !== null) {
        if (Array.isArray(cache[key])) {
          cache[key] = [];
        } else {
          cache[key] = {};
        }
      } else {
        cache[key] = null;
      }
    });
    console.log("Кэш очищен");
  },

  /**
   * Получить статистику кэша
   * @returns {Object} - Статистика использования кэша
   */
  getCacheStats: () => {
    const stats = {};
    Object.keys(cache).forEach((key) => {
      if (cache[key] !== null) {
        if (Array.isArray(cache[key])) {
          stats[key] = `${cache[key].length} элементов`;
        } else if (typeof cache[key] === "object") {
          stats[key] = `${Object.keys(cache[key]).length} ключей`;
        } else {
          stats[key] = "данные загружены";
        }
      } else {
        stats[key] = "пусто";
      }
    });
    return stats;
  },
};

export default ApiService;
