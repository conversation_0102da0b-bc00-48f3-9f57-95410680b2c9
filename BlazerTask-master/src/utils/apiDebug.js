import ApiService from "../services/api.service.js";
import authService from "../services/auth.service.js";

/**
 * Утилиты для отладки API в консоли разработчика
 * Использование в консоли браузера:
 * 
 * // Получить информацию об API
 * window.apiDebug.getInfo()
 * 
 * // Проверить статус авторизации
 * window.apiDebug.checkAuth()
 * 
 * // Очистить кэш
 * window.apiDebug.clearCache()
 * 
 * // Получить статистику кэша
 * window.apiDebug.getCacheStats()
 * 
 * // Тестовый запрос к API
 * window.apiDebug.testApi()
 */

const apiDebug = {
  /**
   * Получить информацию о текущей конфигурации API
   */
  getInfo: () => {
    const info = ApiService.getApiInfo();
    console.group("🔧 API Configuration");
    console.log("Тип API:", info.apiType);
    console.log("Base URL:", info.baseUrl);
    console.log("Images URL:", info.imagesBaseUrl);
    console.log("Авторизация требуется:", info.authRequired);
    if (info.authRequired) {
      console.log("Статус авторизации:", info.authStatus);
    }
    console.log("Endpoints:", info.endpoints);
    console.groupEnd();
    return info;
  },

  /**
   * Проверить статус авторизации
   */
  checkAuth: async () => {
    const status = authService.getAuthStatus();
    console.group("🔐 Auth Status");
    console.log("Требуется авторизация:", status.required);
    console.log("Авторизован:", status.authenticated);
    console.log("Есть токен:", status.hasToken);
    console.log("Токен действителен:", status.tokenValid);
    console.log("Сообщение:", status.message);
    
    if (status.required) {
      try {
        const token = await authService.getAccessToken();
        console.log("Токен получен:", !!token);
        if (token) {
          console.log("Токен (первые 20 символов):", token.substring(0, 20) + "...");
        }
      } catch (error) {
        console.error("Ошибка получения токена:", error);
      }
    }
    console.groupEnd();
    return status;
  },

  /**
   * Очистить кэш
   */
  clearCache: () => {
    ApiService.clearCache();
    console.log("✅ Кэш очищен");
  },

  /**
   * Получить статистику кэша
   */
  getCacheStats: () => {
    const stats = ApiService.getCacheStats();
    console.group("📊 Cache Statistics");
    Object.entries(stats).forEach(([key, value]) => {
      console.log(`${key}:`, value);
    });
    console.groupEnd();
    return stats;
  },

  /**
   * Тестовый запрос к API
   */
  testApi: async () => {
    console.group("🧪 API Test");
    try {
      console.log("Тестируем получение каталога...");
      const catalog = await ApiService.getCatalog();
      console.log("✅ Каталог получен:", catalog?.length || 0, "элементов");
      
      console.log("Тестируем получение продуктов (первая страница)...");
      const products = await ApiService.getProductsWithPagination(1, 5);
      console.log("✅ Продукты получены:", products?.data?.length || 0, "элементов");
      console.log("Пагинация:", products?.pagination);
      
    } catch (error) {
      console.error("❌ Ошибка тестирования API:", error);
    }
    console.groupEnd();
  },

  /**
   * Переключить на основной API (требует перезагрузки)
   */
  switchToProduction: () => {
    console.warn("⚠️ Для переключения на основной API измените USE_PRODUCTION_API на true в src/config/api.js");
    console.log("После изменения перезагрузите страницу");
  },

  /**
   * Переключить на тестовый API (требует перезагрузки)
   */
  switchToTest: () => {
    console.warn("⚠️ Для переключения на тестовый API измените USE_PRODUCTION_API на false в src/config/api.js");
    console.log("После изменения перезагрузите страницу");
  },

  /**
   * Очистить токен авторизации
   */
  clearToken: () => {
    authService.clearToken();
    console.log("✅ Токен авторизации очищен");
  },

  /**
   * Показать справку
   */
  help: () => {
    console.group("📖 API Debug Help");
    console.log("Доступные команды:");
    console.log("window.apiDebug.getInfo() - информация об API");
    console.log("window.apiDebug.checkAuth() - статус авторизации");
    console.log("window.apiDebug.clearCache() - очистить кэш");
    console.log("window.apiDebug.getCacheStats() - статистика кэша");
    console.log("window.apiDebug.testApi() - тестовый запрос");
    console.log("window.apiDebug.clearToken() - очистить токен");
    console.log("window.apiDebug.switchToProduction() - переключить на основной API");
    console.log("window.apiDebug.switchToTest() - переключить на тестовый API");
    console.groupEnd();
  }
};

// Добавляем в глобальную область видимости для удобства отладки
if (typeof window !== 'undefined') {
  window.apiDebug = apiDebug;
  console.log("🔧 API Debug утилиты доступны через window.apiDebug");
  console.log("Введите window.apiDebug.help() для справки");
}

export default apiDebug;
