import React, { useState } from "react";
import styled from "styled-components";
import { useLocation, useNavigate } from "react-router-dom";
import DatePicker, { registerLocale } from "react-datepicker";
import { ru } from "date-fns/locale";
import "react-datepicker/dist/react-datepicker.css";

// Регистрируем русскую локализацию
registerLocale("ru", ru);

const CalendarContainer = styled.div`
  .react-datepicker {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    font-family: inherit;
    background: white;
    padding: 32px;
  }

  .react-datepicker__header {
    background: white;
    border-bottom: none;
    padding: 0 0 20px 0;
  }

  .react-datepicker__current-month {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
  }

  .react-datepicker__navigation {
    top: 32px;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      color: #e9ecef;
    }

    &--previous {
      left: 20px;
    }

    &--next {
      right: 20px;
    }
  }

  .react-datepicker__navigation-icon {
    &::before {
      border-color: #666;
      border-width: 2px 2px 0 0;
      width: 8px;
      height: 8px;
    }
  }

  .react-datepicker__day-names {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-bottom: 10px;
  }

  .react-datepicker__day-name {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    margin: 0;
  }

  .react-datepicker__month {
    /* margin: 0; */
  }

  .react-datepicker__week {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-bottom: 0;
  }

  .react-datepicker__day {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 400;
    color: #333;
    margin: 0;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background: transparent;

    &:hover {
      background: #f0f8ff;
      color: #0066cc;
    }

    &--selected {
      background: #0066cc !important;
      color: white !important;
      font-weight: 600;
    }

    &--today {
      background: #e3f2fd;
      color: #0066cc;
      font-weight: 600;
    }

    &--weekend {
      color: #dc3545;
    }

    &--outside-month {
      color: #ccc;
    }

    &--disabled {
      color: #ccc;
      cursor: not-allowed;

      &:hover {
        background: transparent;
        color: #ccc;
      }
    }
  }

  .react-datepicker__triangle {
    display: none;
  }
`;

const TenderFormContainer = styled.div`
  background-color: white;
  min-height: 100vh;
  padding: 24px 20px;
`;
TenderFormContainer.displayName = "TenderFormContainer";

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
ContentContainer.displayName = "ContentContainer";

const Header = styled.div`
  border-bottom: 1px solid #dfe4e5;
`;
Header.displayName = "Header";

const HeaderContent = styled.div`
  margin: 0 auto;
  max-width: 1150px;
  display: flex;
  padding: 12px 15px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
`;
HeaderContent.displayName = "HeaderContent";

const BackButton = styled.button`
  background-color: white;
  color: #434a54;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
BackButton.displayName = "BackButton";

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
`;
ActionButtons.displayName = "ActionButtons";

const ClearAllButtonContainer = styled.div`
  padding: 16px;
`;
ClearAllButtonContainer.displayName = "ClearAllButtonContainer";

const ClearAllButton = styled.button`
  background-color: white;
  color: #434a54;
  border: 1px solid #d6dce1;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
ClearAllButton.displayName = "ClearAllButton";

const CreateTenderButton = styled.button`
  background-color: #0066cc;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #0055b3;
  }
`;
CreateTenderButton.displayName = "CreateTenderButton";

const Title = styled.h1`
  font-size: 42px;
  font-weight: 900;
  line-height: 1.5;
  color: #434a54;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 24px;
  }
`;
Title.displayName = "Title";

const Text = styled.p`
  font-size: 17px;
  font-weight: 400;
  color: #434a54;
  margin-bottom: 16px;
  line-height: 1.5;
`;
Text.displayName = "Text";

const ProductFormCard = styled.div`
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
`;
ProductFormCard.displayName = "ProductFormCard";

const ProductHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
`;
ProductHeader.displayName = "ProductHeader";

const ProductInfo = styled.div`
  flex: 1;
`;
ProductInfo.displayName = "ProductInfo";

const ProductId = styled.div`
  font-size: 17px;
  color: #969ea7;
  margin-bottom: 10px;
`;
ProductId.displayName = "ProductId";

const Label = styled.div`
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #969ea7;
  margin-bottom: 10px;
`;
Label.displayName = "Label";

const ProductTitle = styled.h3`
  font-size: 24px;
  font-weight: 400;
  color: #434a54;
  line-height: 32px;
  margin-bottom: 10px;
`;
ProductTitle.displayName = "ProductTitle";

const FormRow = styled.div`
  display: flex;
  gap: 24px;
  padding: 10px 0;
  margin-bottom: 16px;
  align-items: flex-end;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;
FormRow.displayName = "FormRow";

const SmallFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  max-width: 200px;
  position: relative;
`;
SmallFormGroup.displayName = "SmallFormGroup";

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  height: 36px;
  padding-right: 40px;

  &:focus {
    border-color: #0066cc;
  }
`;
Input.displayName = "Input";

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-height: 75px;
  width: 100%;
  resize: vertical;
  color: #434a54;
  margin-bottom: 8px;

  &:focus {
    outline: none;
    border-color: #0066cc;
  }
`;
TextArea.displayName = "TextArea";

const ActionButtonContainer = styled.div`
  display: flex;
  gap: 0;
  height: 36px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #0066cc;
`;
ActionButtonContainer.displayName = "ActionButtonContainer";

const ActionButton = styled.button`
  background-color: ${(props) => (props.active ? "#0066cc" : "#f8f9fa")};
  color: ${(props) => (props.active ? "white" : "#434a54")};

  padding: 8px 16px;
  font-size: 17px;
  font-weight: 400;
  cursor: pointer;
  white-space: nowrap;
  height: 36px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.active ? "#0056b3" : "#e9ecef")};
  }
`;
ActionButton.displayName = "ActionButton";

const UploadButton = styled.button`
  background-color: white;
  color: #0066cc;
  border: 1px solid #0066cc;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #e9ecef;
  }
`;
UploadButton.displayName = "UploadButton";

const UploadText = styled.span``;
UploadText.displayName = "UploadText";

const HiddenFileInput = styled.input`
  display: none;
`;
HiddenFileInput.displayName = "HiddenFileInput";

const AttachedFilesList = styled.div`
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;
AttachedFilesList.displayName = "AttachedFilesList";

const AttachedFileItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #434a54;
`;
AttachedFileItem.displayName = "AttachedFileItem";

const FileInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
`;
FileInfo.displayName = "FileInfo";

const FileName = styled.span`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;
FileName.displayName = "FileName";

const FileSize = styled.span`
  color: #969ea7;
  font-size: 12px;
  white-space: nowrap;
`;
FileSize.displayName = "FileSize";

const RemoveFileButton = styled.button`
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  font-size: 24px;
  line-height: 1;
  transition: all 0.3s ease;

  &:hover {
    color: #b21f2d;
  }
`;
RemoveFileButton.displayName = "RemoveFileButton";

const SectionTitle = styled.h2`
  font-size: 24px;
  font-weight: 900;
  color: #434a54;
  margin-top: 42px;
  margin-bottom: 24px;

  &:first-of-type {
    margin-top: 0;
  }
`;
SectionTitle.displayName = "SectionTitle";

const DatePickerContainer = styled.div`
  background: #f5f5f5;
  padding: 48px 0;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
`;
DatePickerContainer.displayName = "DatePickerContainer";

const AddressContainer = styled.div`
  background: white;
  border-radius: 4px;
  margin-bottom: 20px;
`;
AddressContainer.displayName = "AddressContainer";

const TenderNameContainer = styled.div`
  border-radius: 8px;
  margin-bottom: 30px;
  display: flex;
  gap: 16px;
`;
TenderNameContainer.displayName = "TenderNameContainer";

const AddressInputContainer = styled.div`
  position: relative;
  width: 100%;
  margin-top: 8px;
`;
AddressInputContainer.displayName = "AddressInputContainer";

const AddressInput = styled(Input)`
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px 40px 12px 140px;
  font-size: 16px;

  &::placeholder {
    color: #999;
  }

  &:focus {
    outline: none;
    border-color: #e0e0e0;
  }
`;
AddressInput.displayName = "AddressInput";

const CitySelector = styled.div`
  position: absolute;
  left: 1px;
  top: 1px;
  bottom: 1px;
  width: 130px;
`;
CitySelector.displayName = "CitySelector";

const CityDropdown = styled.div`
  position: relative;
  height: 100%;
`;
CityDropdown.displayName = "CityDropdown";

const CityButton = styled.button`
  background: white;
  border: none;
  border-right: 1px solid #e0e0e0;
  border-radius: 3px 0 0 3px;
  padding: 12px 16px;
  font-size: 16px;
  color: #333;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;

  &:hover {
    background-color: #f8f9fa;
  }

  &:focus {
    outline: none;
  }

  &:after {
    content: "▼";
    font-size: 12px;
    color: #666;
    transition: transform 0.2s ease;
    transform: ${(props) => (props.isOpen ? "rotate(180deg)" : "rotate(0deg)")};
  }
`;
CityButton.displayName = "CityButton";

const CityDropdownList = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  width: 200px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
`;
CityDropdownList.displayName = "CityDropdownList";

const ClearButton = styled.button`
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;

  &:hover {
    color: #666;
  }
`;
ClearButton.displayName = "ClearButton";

const CityOption = styled.div`
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
`;
CityOption.displayName = "CityOption";

const CityCheckbox = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid ${(props) => (props.checked ? "#0066cc" : "#e0e0e0")};
  border-radius: 4px;
  background: ${(props) => (props.checked ? "#0066cc" : "white")};
  display: flex;
  align-items: center;
  justify-content: center;

  &:after {
    content: "✓";
    color: white;
    font-size: 14px;
    font-weight: bold;
    display: ${(props) => (props.checked ? "block" : "none")};
  }
`;
CityCheckbox.displayName = "CityCheckbox";

const TenderInputContainer = styled.div`
  position: relative;
  flex: 1;
`;
TenderInputContainer.displayName = "TenderInputContainer";

const TenderNameInput = styled(Input)`
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 16px;

  &::placeholder {
    color: #999;
  }

  &:focus {
    outline: none;
    border-color: #e0e0e0;
  }
`;
TenderNameInput.displayName = "TenderNameInput";

const TenderNumberInput = styled(Input)`
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 16px;

  &::placeholder {
    color: #999;
  }

  &:focus {
    outline: none;
    border-color: #e0e0e0;
  }
`;
TenderNumberInput.displayName = "TenderNumberInput";

const TenderClearButton = styled.button`
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #ccc;
  cursor: pointer;
  font-size: 20px;
  padding: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #999;
  }
`;
TenderClearButton.displayName = "TenderClearButton";

const SubmitButton = styled.button`
  width: 100%;
  background-color: #068948;
  color: white;
  border: none;
  padding: 17px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #057a3f;
  }
`;
SubmitButton.displayName = "SubmitButton";

const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const TenderForm = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Получаем товары из state или из localStorage
  const selectedProducts = (() => {
    // Сначала пробуем получить из переданного state
    if (location.state?.selectedProducts?.length > 0) {
      return location.state.selectedProducts;
    }

    // Если нет в state, пробуем загрузить из localStorage
    try {
      const saved = localStorage.getItem("selectedTenderProducts");
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.error("Ошибка при загрузке данных из localStorage:", error);
      return [];
    }
  })();

  const [formData, setFormData] = useState(
    selectedProducts.map((product) => ({
      productId: product.MaterialId,
      productName: product.MaterialName,
      quantity: "",
      unit: "шт",
      maxPrice: "",
      description: "",
      analogAllowed: true, // true - можно аналог (по умолчанию), false - только это
      attachedFiles: [], // массив прикрепленных файлов
    }))
  );

  const [deliveryDate, setDeliveryDate] = useState(null);
  const [deliveryAddress, setDeliveryAddress] = useState("");
  const [selectedCity, setSelectedCity] = useState("Алматы");
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const [tenderName, setTenderName] = useState("");
  const [tenderNumber, setTenderNumber] = useState("");
  const [deliveryPriceIncluded, setDeliveryPriceIncluded] = useState(true); // true - с доставкой, false - без доставки

  const cities = ["Нур-Султан", "Алматы", "Аксай", "Актау", "Актобе"];

  const handleProductChange = (index, field, value) => {
    const newFormData = [...formData];
    newFormData[index][field] = value;
    setFormData(newFormData);
  };

  const handleAnalogChoice = (index, analogAllowed) => {
    const newFormData = [...formData];
    newFormData[index].analogAllowed = analogAllowed;
    setFormData(newFormData);
  };

  const handleDeliveryPriceChoice = (includeDelivery) => {
    setDeliveryPriceIncluded(includeDelivery);
  };

  const handleCitySelect = (city) => {
    setSelectedCity(city);
    setIsCityDropdownOpen(false);
  };

  const toggleCityDropdown = () => {
    setIsCityDropdownOpen(!isCityDropdownOpen);
  };

  const handleFileUpload = (index, files) => {
    const newFormData = [...formData];
    const fileArray = Array.from(files);

    // Добавляем новые файлы к существующим
    newFormData[index].attachedFiles = [
      ...newFormData[index].attachedFiles,
      ...fileArray,
    ];

    setFormData(newFormData);
  };

  const handleRemoveFile = (productIndex, fileIndex) => {
    const newFormData = [...formData];
    newFormData[productIndex].attachedFiles = newFormData[
      productIndex
    ].attachedFiles.filter((_, index) => index !== fileIndex);
    setFormData(newFormData);
  };

  const handleRemoveProduct = (index) => {
    const newFormData = formData.filter((_, i) => i !== index);
    setFormData(newFormData);

    // Получаем ID удаляемого товара
    const removedProductId = formData[index].productId;

    // Обновляем localStorage - убираем товар из списка выбранных
    try {
      const saved = localStorage.getItem("selectedTenderProducts");
      if (saved) {
        const selectedProducts = JSON.parse(saved);
        const updatedProducts = selectedProducts.filter(
          (product) => product.MaterialId !== removedProductId
        );
        localStorage.setItem(
          "selectedTenderProducts",
          JSON.stringify(updatedProducts)
        );

        // Отправляем кастомное событие для синхронизации
        window.dispatchEvent(
          new CustomEvent("localStorageChange", {
            detail: { key: "selectedTenderProducts", value: updatedProducts },
          })
        );

        // Если это был последний товар, перенаправляем на CreateTender
        if (updatedProducts.length === 0) {
          navigate("/create-tender");
          return;
        }
      }
    } catch (error) {
      console.error("Ошибка при обновлении localStorage:", error);
    }
  };

  const handleSubmit = () => {
    const tenderData = {
      name: tenderName,
      products: formData,
      deliveryDate,
      deliveryAddress,
      deliveryPriceIncluded, // включена ли цена доставки
    };

    console.log("Создание тендера:", tenderData);

    // Очищаем localStorage после успешного создания тендера
    try {
      localStorage.removeItem("selectedTenderProducts");
      console.log("Данные очищены из localStorage");
    } catch (error) {
      console.error("Ошибка при очистке localStorage:", error);
    }

    // Здесь будет логика отправки данных на сервер
    alert("Тендер успешно создан!");

    // Возвращаемся на главную страницу
    navigate("/");
  };

  const handleClearAll = () => {
    // Очищаем localStorage
    try {
      localStorage.removeItem("selectedTenderProducts");

      // Отправляем кастомное событие для синхронизации
      window.dispatchEvent(
        new CustomEvent("localStorageChange", {
          detail: { key: "selectedTenderProducts", value: [] },
        })
      );
    } catch (error) {
      console.error("Ошибка при очистке localStorage:", error);
    }

    // Перенаправляем на CreateTender
    navigate("/create-tender");
  };

  const handleBack = () => {
    navigate(-1);
  };

  if (selectedProducts.length === 0) {
    return (
      <TenderFormContainer>
        <ContentContainer>
          <Title>Нет выбранных товаров для создания тендера</Title>
          <BackButton onClick={handleBack}>ВЕРНУТЬСЯ К ПОИСКУ</BackButton>
        </ContentContainer>
      </TenderFormContainer>
    );
  }

  return (
    <>
      <Header>
        <HeaderContent>
          <BackButton onClick={handleBack}>
            <img src="/icons/arrow_back_24px.svg" />
            ВЕРНУТЬСЯ К ПОИСКУ
          </BackButton>

          <ActionButtons>
            <ClearAllButton onClick={handleClearAll}>
              <img
                src="/icons/BusketCreateTender.svg"
                width={"13"}
                height={"13"}
              />
              ОЧИСТИТЬ ВСЕ
            </ClearAllButton>
            <CreateTenderButton onClick={handleSubmit}>
              СОЗДАТЬ ТЕНДЕР
              <img
                src="/icons/CheckCreateTender.svg"
                width={"15"}
                height={"15"}
              />
            </CreateTenderButton>
          </ActionButtons>
        </HeaderContent>
      </Header>
      <TenderFormContainer>
        <ContentContainer>
          <Title>Создание тендера на закуп</Title>

          <SectionTitle>Детали закупки</SectionTitle>

          <Text>
            Укажите необходимо количество позиций, цену которую готовы <br />
            предложить и пожелания к заказу.
          </Text>

          {formData.map((product, index) => (
            <ProductFormCard key={product.productId}>
              <ProductHeader>
                <ProductInfo>
                  <ProductId>{product.productId}</ProductId>
                  <Label>Единица измерения: Штуки</Label>
                  <ProductTitle>{product.productName}</ProductTitle>
                  <Label style={{ marginBottom: 0 }}>
                    Продается в штуках, используется для выкладки стен и иных
                    несущих конструкций.
                  </Label>
                </ProductInfo>
                <ClearAllButtonContainer>
                  <ClearAllButton
                    onClick={() => handleRemoveProduct(index)}
                    style={{ padding: "12px" }}
                  >
                    <img
                      src="/icons/BusketCreateTender.svg"
                      width={"16"}
                      height={"16"}
                    />
                  </ClearAllButton>
                </ClearAllButtonContainer>
              </ProductHeader>

              <FormRow>
                <SmallFormGroup>
                  <Input
                    type="number"
                    value={product.quantity}
                    onChange={(e) =>
                      handleProductChange(index, "quantity", e.target.value)
                    }
                    placeholder="Кол-во"
                  />
                  <span
                    style={{
                      position: "absolute",
                      right: "8px",
                      bottom: "8px",
                      color: "#656D78",
                      fontSize: "17px",
                      fontWeight: "400",
                    }}
                  >
                    шт.
                  </span>
                </SmallFormGroup>

                <SmallFormGroup>
                  <Input
                    type="number"
                    value={product.maxPrice}
                    onChange={(e) =>
                      handleProductChange(index, "maxPrice", e.target.value)
                    }
                    placeholder="Ваша цена"
                    style={{ paddingRight: "77px" }}
                  />
                  <span
                    style={{
                      position: "absolute",
                      right: "8px",
                      bottom: "8px",
                      color: "#656D78",
                      fontSize: "17px",
                      fontWeight: "400",
                    }}
                  >
                    ₸ за шт.
                  </span>
                </SmallFormGroup>

                <ActionButtonContainer>
                  <ActionButton
                    active={product.analogAllowed === true}
                    onClick={() => handleAnalogChoice(index, true)}
                  >
                    Можно аналог
                  </ActionButton>
                  <ActionButton
                    active={product.analogAllowed === false}
                    onClick={() => handleAnalogChoice(index, false)}
                  >
                    Только это
                  </ActionButton>
                </ActionButtonContainer>
              </FormRow>

              <Label style={{ fontSize: "17px", color: "#656D78" }}>
                Пожелания к заказу
              </Label>

              <TextArea
                value={product.description}
                onChange={(e) =>
                  handleProductChange(index, "description", e.target.value)
                }
                placeholder="Например: Необходимо предоставить цену с доставкой до указанного адреса, отгрузить силами поставщика."
              />

              <UploadButton
                onClick={() =>
                  document.getElementById(`file-input-${index}`).click()
                }
              >
                <img src="/icons/Upload.svg" />
                <UploadText>Прикрепить файл</UploadText>
              </UploadButton>

              <HiddenFileInput
                id={`file-input-${index}`}
                type="file"
                multiple
                onChange={(e) => handleFileUpload(index, e.target.files)}
              />

              {product.attachedFiles.length > 0 && (
                <AttachedFilesList>
                  {product.attachedFiles.map((file, fileIndex) => (
                    <AttachedFileItem key={fileIndex}>
                      <FileInfo>
                        <FileName>{file.name}</FileName>
                        <FileSize>{formatFileSize(file.size)}</FileSize>
                      </FileInfo>
                      <RemoveFileButton
                        onClick={() => handleRemoveFile(index, fileIndex)}
                        title="Удалить файл"
                      >
                        ×
                      </RemoveFileButton>
                    </AttachedFileItem>
                  ))}
                </AttachedFilesList>
              )}
            </ProductFormCard>
          ))}

          <SectionTitle>Учесть цену доставки в предложениях?</SectionTitle>
          <Label style={{ marginBottom: "16px" }}>
            Мы предупредим поставщиков о необходимости подачи цены <br />с
            учетом доставки до вашего адреса.
          </Label>
          <ActionButtonContainer style={{ width: "fit-content" }}>
            <ActionButton
              active={deliveryPriceIncluded === true}
              onClick={() => handleDeliveryPriceChoice(true)}
            >
              Цена с доставкой
            </ActionButton>
            <ActionButton
              active={deliveryPriceIncluded === false}
              onClick={() => handleDeliveryPriceChoice(false)}
            >
              Цена без доставки
            </ActionButton>
          </ActionButtonContainer>

          <SectionTitle>Срок поставки</SectionTitle>
          <Label style={{ marginBottom: "16px" }}>
            {" "}
            Укажите дату когда необходимо осуществить поставку.
          </Label>
          <DatePickerContainer>
            <CalendarContainer>
              <DatePicker
                selected={deliveryDate}
                onChange={(date) => setDeliveryDate(date)}
                dateFormat="dd MMMM yyyy"
                locale="ru"
                calendarStartDay={1}
                inline
              />
            </CalendarContainer>
          </DatePickerContainer>

          <SectionTitle>Адрес доставки</SectionTitle>
          <Label style={{ marginBottom: "16px" }}>
            Укажите адрес куда необходимо доставить товар, или где его забрать
          </Label>
          <AddressContainer>
            <AddressInputContainer>
              <AddressInput
                type="text"
                value={deliveryAddress}
                onChange={(e) => setDeliveryAddress(e.target.value)}
                placeholder="Название улицы, дом, строение"
              />
              {deliveryAddress && (
                <ClearButton onClick={() => setDeliveryAddress("")}>
                  ×
                </ClearButton>
              )}
              <CitySelector>
                <CityDropdown>
                  <CityButton
                    onClick={toggleCityDropdown}
                    isOpen={isCityDropdownOpen}
                  >
                    {selectedCity}
                  </CityButton>
                  {isCityDropdownOpen && (
                    <CityDropdownList>
                      {cities.map((city) => (
                        <CityOption
                          key={city}
                          onClick={() => handleCitySelect(city)}
                        >
                          <span>{city}</span>
                          <CityCheckbox checked={selectedCity === city} />
                        </CityOption>
                      ))}
                    </CityDropdownList>
                  )}
                </CityDropdown>
              </CitySelector>
            </AddressInputContainer>
          </AddressContainer>

          <SectionTitle>Название и номер закупки</SectionTitle>
          <Label style={{ marginBottom: "16px" }}>
            Придумайте название закупки для удобства в списке ваших закупок
          </Label>
          <TenderNameContainer>
            <TenderInputContainer>
              <TenderNameInput
                type="text"
                value={tenderName}
                onChange={(e) => setTenderName(e.target.value)}
                placeholder="Название закупки"
              />
              {tenderName && (
                <TenderClearButton onClick={() => setTenderName("")}>
                  ×
                </TenderClearButton>
              )}
            </TenderInputContainer>
            <TenderInputContainer>
              <TenderNumberInput
                type="text"
                value={tenderNumber}
                onChange={(e) => setTenderNumber(e.target.value)}
                placeholder="№ Закупки"
              />
              {tenderNumber && (
                <TenderClearButton onClick={() => setTenderNumber("")}>
                  ×
                </TenderClearButton>
              )}
            </TenderInputContainer>
          </TenderNameContainer>

          <SubmitButton onClick={handleSubmit}>
            Опубликовать тендер
          </SubmitButton>
        </ContentContainer>
      </TenderFormContainer>
    </>
  );
};

export default TenderForm;
