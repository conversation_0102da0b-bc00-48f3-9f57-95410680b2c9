# План миграции React CRA → Next.js с SSR для SEO

## 📋 Обзор проекта

**Текущая архитектура:**
- React 19.1.0 + Create React App
- React Router DOM 7.6.0 для роутинга
- Styled Components 6.1.17 для стилей
- TanStack React Query 5.76.1 для управления состоянием сервера
- Context API для корзины
- REST API с авторизацией через Bearer токены

**Цель миграции:** Добавить SSR для улучшения SEO и производительности

## 🎯 Этапы миграции

### Этап 1: Подготовка и настройка Next.js (1-2 дня)

#### 1.1 Создание нового Next.js проекта
```bash
npx create-next-app@latest nursadi-nextjs --typescript --tailwind --eslint --app
cd nursadi-nextjs
```

#### 1.2 Установка зависимостей
```bash
npm install @tanstack/react-query @tanstack/react-query-devtools
npm install styled-components date-fns react-datepicker
npm install react-virtualized-auto-sizer react-window react-window-infinite-loader
```

#### 1.3 Настройка конфигурации
- `next.config.js` - настройка styled-components, изображений, API proxy
- `tsconfig.json` - настройка путей и алиасов
- `.env.local` - переменные окружения для API

### Этап 2: Миграция структуры файлов (2-3 дня)

#### 2.1 Новая структура папок
```
src/
├── app/                    # App Router (Next.js 13+)
│   ├── layout.tsx         # Корневой layout
│   ├── page.tsx           # Главная страница (PriceList)
│   ├── product/
│   │   └── [id]/
│   │       └── page.tsx   # Страница товара
│   ├── cart/
│   │   └── page.tsx       # Корзина
│   ├── auth/
│   │   └── page.tsx       # Авторизация
│   ├── create-tender/
│   │   └── page.tsx       # Создание тендера
│   └── tender-form/
│       └── page.tsx       # Форма тендера
├── components/            # Компоненты (без изменений)
├── hooks/                 # Хуки (без изменений)
├── services/              # API сервисы (без изменений)
├── context/               # Context API (без изменений)
├── utils/                 # Утилиты (без изменений)
└── config/                # Конфигурация (без изменений)
```

#### 2.2 Миграция компонентов
- Все компоненты остаются без изменений
- Добавить `'use client'` для клиентских компонентов
- Обновить импорты для новой структуры

### Этап 3: Настройка роутинга (1-2 дня)

#### 3.1 Замена React Router на App Router
- Удалить `react-router-dom`
- Создать файловую структуру роутинга Next.js
- Заменить `useNavigate` на `useRouter` из Next.js
- Заменить `Link` компоненты на Next.js `Link`

#### 3.2 Обновление навигации
```typescript
// Было (React Router)
import { useNavigate, Link } from 'react-router-dom';

// Стало (Next.js)
import { useRouter } from 'next/navigation';
import Link from 'next/link';
```

### Этап 4: Настройка SSR и SEO (2-3 дня)

#### 4.1 Создание серверных компонентов
```typescript
// app/page.tsx - Главная страница с SSR
export default async function HomePage() {
  const initialProducts = await ApiService.getProductsWithPagination(1, 20);
  
  return (
    <PriceListClient initialData={initialProducts} />
  );
}

// app/product/[id]/page.tsx - Страница товара с SSR
export default async function ProductPage({ params }: { params: { id: string } }) {
  const product = await ApiService.getProductById(params.id);
  
  return (
    <ProductDetailClient product={product} />
  );
}
```

#### 4.2 Настройка метаданных для SEO
```typescript
// app/layout.tsx
export const metadata: Metadata = {
  title: 'SADI.KZ - Строительные материалы',
  description: 'Строительные материалы инструменты оборудование',
};

// app/product/[id]/page.tsx
export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const product = await ApiService.getProductById(params.id);
  
  return {
    title: `${product.MaterialName} - SADI.KZ`,
    description: `Купить ${product.MaterialName}. Цена от ${product.RetailPrice} ₸`,
    openGraph: {
      title: product.MaterialName,
      description: `Цена от ${product.RetailPrice} ₸`,
      images: [product.imageUrl],
    },
  };
}
```

### Этап 5: Настройка API и состояния (1-2 дня)

#### 5.1 Настройка React Query для SSR
```typescript
// app/providers.tsx
'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

export default function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 30 * 60 * 1000,
        cacheTime: 60 * 60 * 1000,
      },
    },
  }));

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
```

#### 5.2 Настройка API routes (опционально)
```typescript
// app/api/products/route.ts
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const page = searchParams.get('page') || '1';
  const limit = searchParams.get('limit') || '20';
  
  const products = await ApiService.getProductsWithPagination(
    parseInt(page), 
    parseInt(limit)
  );
  
  return Response.json(products);
}
```

### Этап 6: Оптимизация и производительность (1-2 дня)

#### 6.1 Настройка изображений
```typescript
// next.config.js
module.exports = {
  images: {
    domains: ['sadi.kz', 'test.sadi.kz'],
    formats: ['image/webp', 'image/avif'],
  },
};

// Замена img на Next.js Image
import Image from 'next/image';

<Image
  src={imageUrl}
  alt={product.MaterialName}
  width={300}
  height={200}
  priority={index < 4} // Приоритет для первых 4 изображений
/>
```

#### 6.2 Настройка кэширования
```typescript
// app/product/[id]/page.tsx
export const revalidate = 3600; // Ревалидация каждый час

// Или динамическое кэширование
export const dynamic = 'force-dynamic'; // Для страниц с пользовательскими данными
```

### Этап 7: Тестирование и отладка (2-3 дня)

#### 7.1 Функциональное тестирование
- [ ] Проверка всех страниц и роутов
- [ ] Тестирование поиска и фильтрации
- [ ] Проверка корзины и создания тендеров
- [ ] Тестирование авторизации

#### 7.2 SEO тестирование
- [ ] Проверка метатегов
- [ ] Тестирование Open Graph
- [ ] Проверка структурированных данных
- [ ] Тестирование в Google Search Console

#### 7.3 Производительность
- [ ] Lighthouse аудит
- [ ] Core Web Vitals
- [ ] Тестирование скорости загрузки

### Этап 8: Деплой и мониторинг (1 день)

#### 8.1 Настройка деплоя
- Vercel (рекомендуется для Next.js)
- Или Netlify с настройкой SSR
- Настройка переменных окружения

#### 8.2 Мониторинг
- Настройка аналитики
- Мониторинг ошибок (Sentry)
- Отслеживание производительности

## 🔧 Технические детали

### Конфигурация styled-components для SSR
```javascript
// next.config.js
const nextConfig = {
  compiler: {
    styledComponents: true,
  },
};
```

### Настройка TypeScript (опционально)
```bash
npm install --save-dev typescript @types/react @types/node
```

### Обновление package.json
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  }
}
```

## ⚠️ Потенциальные проблемы

1. **Styled Components SSR** - требует настройки для корректной работы
2. **localStorage** - недоступен на сервере, нужна проверка `typeof window`
3. **React Query** - требует настройки hydration для SSR
4. **Динамические импорты** - некоторые компоненты могут требовать `dynamic` импорта

## 📊 Ожидаемые результаты

- **SEO**: Улучшение индексации поисковыми системами
- **Производительность**: Быстрая загрузка первой страницы
- **UX**: Мгновенная навигация между страницами
- **Core Web Vitals**: Улучшение показателей LCP, FID, CLS

## ⏱️ Общее время: 10-15 дней

**Критический путь:**
1. Настройка Next.js (2 дня)
2. Миграция компонентов (3 дня)
3. Настройка SSR (3 дня)
4. Тестирование (3 дня)
5. Деплой (1 день)

**Рекомендация:** Выполнять миграцию поэтапно с тестированием каждого этапа.
