/** @type {import('next').NextConfig} */
const nextConfig = {
  // Настройка styled-components для SSR
  compiler: {
    styledComponents: true,
  },

  // Настройка изображений
  images: {
    domains: ["sadi.kz", "test.sadi.kz"],
    formats: ["image/webp", "image/avif"],
  },

  // Настройка переменных окружения
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // Настройка для статических файлов
  trailingSlash: false,

  // Настройка для оптимизации
  swcMinify: true,

  // Экспериментальные функции
  experimental: {
    // Включаем App Router (по умолчанию в Next.js 13+)
    appDir: true,
  },
};

module.exports = nextConfig;
