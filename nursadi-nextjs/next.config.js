/** @type {import('next').NextConfig} */
const nextConfig = {
  // Настройка styled-components для SSR
  compiler: {
    styledComponents: true,
  },

  // Настройка изображений
  images: {
    domains: ["sadi.kz", "test.sadi.kz"],
    formats: ["image/webp", "image/avif"],
  },

  // Настройка переменных окружения
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // Настройка для статических файлов
  trailingSlash: false,

  // Отключаем ESLint во время сборки
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Отключаем TypeScript проверки во время сборки
  typescript: {
    ignoreBuildErrors: true,
  },
};

module.exports = nextConfig;
