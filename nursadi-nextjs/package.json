{"name": "nursadi-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tanstack/react-query": "^5.80.7", "@tanstack/react-query-devtools": "^5.80.7", "date-fns": "^4.1.0", "next": "15.3.3", "react": "^19.0.0", "react-datepicker": "^8.4.0", "react-dom": "^19.0.0", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "styled-components": "^6.1.19"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^19", "@types/react-virtualized-auto-sizer": "^1.0.4", "@types/react-window": "^1.8.8", "@types/react-window-infinite-loader": "^1.0.9", "@types/styled-components": "^5.1.34", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}