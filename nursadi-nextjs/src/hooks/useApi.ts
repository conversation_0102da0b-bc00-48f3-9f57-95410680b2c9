import { useQuery } from "@tanstack/react-query";
import ApiService from "../services/api.service";

/**
 * Хук для получения продуктов с пагинацией
 * @param {number} page - Номер страницы
 * @param {number} pageSize - Размер страницы
 * @param {Object} options - Опции для useQuery
 * @returns {Object} - Результат запроса с пагинацией
 */
export const usePaginatedProducts = (
  page: number = 1,
  pageSize: number = 20,
  options: any = {}
) => {
  return useQuery({
    queryKey: ["paginatedProducts", page, pageSize],
    queryFn: () => ApiService.getProductsWithPagination(page, pageSize),
    ...options,
  });
};

/**
 * Хук для получения продуктов по категории с пагинацией
 * @param {Object} category - Объект категории с полями department, section, subsection, group
 * @param {number} page - Номер страницы
 * @param {number} pageSize - Размер страницы
 * @param {Object} options - Опции для useQuery
 * @returns {Object} - Результат запроса с пагинацией
 */
export const usePaginatedFilteredProducts = (
  category: any,
  page: number = 1,
  pageSize: number = 20,
  options: any = {}
) => {
  // Формируем ключ запроса на основе выбранной категории
  const queryKey = [
    "paginatedFilteredProducts",
    category?.department || "",
    category?.section || "",
    category?.subsection || "",
    category?.group || "",
    page,
    pageSize,
  ];

  const isEnabled = Boolean(
    category &&
      (category.department ||
        category.section ||
        category.subsection ||
        category.group)
  );

  const { enabled: _, ...restOptions } = options;

  return useQuery({
    queryKey: queryKey,
    queryFn: () =>
      ApiService.getProductsWithPagination(page, pageSize, category?.code),
    enabled: isEnabled,
    ...restOptions,
  });
};

/**
 * Хук для поиска продуктов
 * @param {string} query - Поисковый запрос
 * @param {Object} options - Опции для useQuery
 * @returns {Object} - Результат запроса
 */
export const useSearchProducts = (query: string, options: any = {}) => {
  const { enabled: _, ...restOptions } = options;

  return useQuery({
    queryKey: ["searchProducts", query],
    queryFn: () => ApiService.searchProducts(query),
    enabled: Boolean(query && query.trim()),
    ...restOptions,
  });
};

/**
 * Хук для поиска продуктов с пагинацией
 * @param {string} query - Поисковый запрос
 * @param {number} page - Номер страницы
 * @param {number} pageSize - Размер страницы
 * @param {Object} options - Опции для useQuery
 * @returns {Object} - Результат запроса с пагинацией
 */
export const usePaginatedSearchProducts = (
  query: string,
  page: number = 1,
  pageSize: number = 20,
  options: any = {}
) => {
  const { enabled: _, ...restOptions } = options;

  return useQuery({
    queryKey: ["paginatedSearchProducts", query, page, pageSize],
    queryFn: () =>
      ApiService.searchProductsWithPagination(query, page, pageSize),
    enabled: Boolean(query && query.trim()),
    ...restOptions,
  });
};

/**
 * Хук для поиска продуктов с категорией и пагинацией
 * @param {string} query - Поисковый запрос
 * @param {Object} category - Объект категории
 * @param {number} page - Номер страницы
 * @param {number} pageSize - Размер страницы
 * @param {Object} options - Опции для useQuery
 * @returns {Object} - Результат запроса с пагинацией
 */
export const usePaginatedSearchWithCategory = (
  query: string,
  category: any,
  page: number = 1,
  pageSize: number = 20,
  options: any = {}
) => {
  // Формируем ключ запроса на основе поискового запроса и выбранной категории
  const queryKey = [
    "paginatedSearchWithCategory",
    query,
    category?.department || "",
    category?.section || "",
    category?.subsection || "",
    category?.group || "",
    page,
    pageSize,
  ];

  const { enabled: _, ...restOptions } = options;

  return useQuery({
    queryKey: queryKey,
    queryFn: () =>
      ApiService.getProductsWithPagination(
        page,
        pageSize,
        category?.code,
        query
      ),
    enabled: Boolean(
      query &&
        query.trim() &&
        category &&
        (category.department ||
          category.section ||
          category.subsection ||
          category.group)
    ),
    ...restOptions,
  });
};

/**
 * Хук для получения информации о продукте по ID
 * @param {string} productId - ID продукта
 * @param {Object} options - Опции для useQuery
 * @returns {Object} - Результат запроса
 */
export const useProduct = (productId: string, options: any = {}) => {
  const { enabled: _, ...restOptions } = options;

  return useQuery({
    queryKey: ["product", productId],
    queryFn: () => ApiService.getProductById(productId),
    enabled: Boolean(productId),
    ...restOptions,
  });
};

/**
 * Хук для получения каталога
 * @param {Object} options - Опции для useQuery
 * @returns {Object} - Результат запроса
 */
export const useCatalog = (options: any = {}) => {
  return useQuery({
    queryKey: ["catalog"],
    queryFn: () => ApiService.getCatalog(),
    ...options,
  });
};
