import API_CONFIG from "../config/api";

/**
 * Сервис для работы с авторизацией и токенами
 */
class AuthService {
  private tokenKey = "api_access_token";
  private tokenExpiryKey = "api_token_expiry";

  /**
   * Получить токен доступа
   * @returns {Promise<string|null>} - Токен доступа или null
   */
  async getAccessToken(): Promise<string | null> {
    // Если авторизация не требуется, возвращаем null
    if (!API_CONFIG.AUTH_REQUIRED) {
      return null;
    }

    // Проверяем, есть ли действующий токен в localStorage
    const existingToken = this.getStoredToken();
    if (existingToken && this.isTokenValid()) {
      console.log("Используется существующий токен");
      return existingToken;
    }

    // Если токена нет или он истек, получаем новый
    console.log("Получение нового токена...");
    return await this.requestNewToken();
  }

  /**
   * Получить сохраненный токен из localStorage
   * @returns {string|null} - Токен или null
   */
  private getStoredToken(): string | null {
    if (typeof window === 'undefined') return null;
    
    try {
      return localStorage.getItem(this.tokenKey);
    } catch (error) {
      console.error("Ошибка при получении токена из localStorage:", error);
      return null;
    }
  }

  /**
   * Проверить, действителен ли токен
   * @returns {boolean} - true, если токен действителен
   */
  private isTokenValid(): boolean {
    if (typeof window === 'undefined') return false;
    
    try {
      const expiryTime = localStorage.getItem(this.tokenExpiryKey);
      if (!expiryTime) return false;

      const now = new Date().getTime();
      return now < parseInt(expiryTime, 10);
    } catch (error) {
      console.error("Ошибка при проверке срока действия токена:", error);
      return false;
    }
  }

  /**
   * Запросить новый токен с сервера
   * @returns {Promise<string|null>} - Новый токен или null при ошибке
   */
  private async requestNewToken(): Promise<string | null> {
    try {
      if (!API_CONFIG.TOKEN_URL || !API_CONFIG.AUTH_CREDENTIALS) {
        throw new Error("Не настроены параметры авторизации");
      }

      // Подготавливаем данные для запроса токена
      const formData = new URLSearchParams();
      Object.entries(API_CONFIG.AUTH_CREDENTIALS).forEach(([key, value]) => {
        formData.append(key, value);
      });

      console.log(`Запрос токена: ${API_CONFIG.TOKEN_URL}`);

      const response = await fetch(API_CONFIG.TOKEN_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (!data.access_token) {
        throw new Error("Токен не получен от сервера");
      }

      // Сохраняем токен и время истечения
      this.saveToken(data.access_token, data.expires_in);

      console.log("Новый токен получен и сохранен");
      return data.access_token;
    } catch (error) {
      console.error("Ошибка при получении токена:", error);
      return null;
    }
  }

  /**
   * Сохранить токен в localStorage
   * @param {string} token - Токен для сохранения
   * @param {number} expiresIn - Время жизни токена в секундах
   */
  private saveToken(token: string, expiresIn: number): void {
    if (typeof window === 'undefined') return;
    
    try {
      // Сохраняем токен
      localStorage.setItem(this.tokenKey, token);

      // Вычисляем время истечения (с запасом в 5 минут)
      const expiryTime = new Date().getTime() + (expiresIn - 300) * 1000;
      localStorage.setItem(this.tokenExpiryKey, expiryTime.toString());

      console.log(`Токен сохранен, истекает: ${new Date(expiryTime).toLocaleString()}`);
    } catch (error) {
      console.error("Ошибка при сохранении токена:", error);
    }
  }

  /**
   * Очистить сохраненный токен
   */
  clearToken(): void {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.removeItem(this.tokenKey);
      localStorage.removeItem(this.tokenExpiryKey);
      console.log("Токен очищен");
    } catch (error) {
      console.error("Ошибка при очистке токена:", error);
    }
  }

  /**
   * Получить заголовки авторизации для запросов
   * @returns {Promise<Object>} - Объект с заголовками
   */
  async getAuthHeaders(): Promise<Record<string, string>> {
    if (!API_CONFIG.AUTH_REQUIRED) {
      return {};
    }

    const token = await this.getAccessToken();
    if (!token) {
      console.warn("Не удалось получить токен авторизации");
      return {};
    }

    return {
      Authorization: `Bearer ${token}`,
    };
  }

  /**
   * Проверить, требуется ли авторизация
   * @returns {boolean} - true, если требуется авторизация
   */
  isAuthRequired(): boolean {
    return API_CONFIG.AUTH_REQUIRED;
  }

  /**
   * Получить информацию о текущем состоянии авторизации
   * @returns {Object} - Информация о состоянии авторизации
   */
  getAuthStatus() {
    if (!API_CONFIG.AUTH_REQUIRED) {
      return {
        required: false,
        authenticated: true,
        message: "Авторизация не требуется (тестовый API)",
      };
    }

    const token = this.getStoredToken();
    const isValid = this.isTokenValid();

    return {
      required: true,
      authenticated: token && isValid,
      hasToken: !!token,
      tokenValid: isValid,
      message: token && isValid 
        ? "Авторизован" 
        : token 
          ? "Токен истек" 
          : "Токен отсутствует",
    };
  }
}

// Создаем единственный экземпляр сервиса
const authService = new AuthService();

export default authService;
