import API_CONFIG from "../config/api";
import authService from "./auth.service";

// Кэш для хранения данных
const cache: Record<string, any> = {};

/**
 * Универсальная функция для выполнения запросов к API
 * @param {string} url - URL для запроса
 * @param {Object} options - Опции для fetch
 * @param {string} cacheKey - Ключ для кэширования (опционально)
 * @returns {Promise<any>} - Результат запроса
 */
async function fetchApi(
  url: string,
  options: RequestInit = {},
  cacheKey?: string
): Promise<any> {
  // Проверяем кэш, если указан ключ
  if (cacheKey && cache[cacheKey]) {
    console.log(`Данные получены из кэша: ${cacheKey}`);
    return cache[cacheKey];
  }

  try {
    console.log(`Запрос к API: ${url}`);

    // Получаем заголовки авторизации
    const authHeaders = await authService.getAuthHeaders();

    // Объединяем заголовки
    const headers = {
      ...API_CONFIG.REQUEST_OPTIONS.headers,
      ...authHeaders,
      ...options.headers,
    };

    const response = await fetch(url, {
      ...API_CONFIG.REQUEST_OPTIONS,
      ...options,
      headers,
    });

    if (!response.ok) {
      // Если ошибка авторизации, очищаем токен
      if (response.status === 401 && authService.isAuthRequired()) {
        console.warn("Ошибка авторизации, очищаем токен");
        authService.clearToken();
      }
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();

    // Сохраняем в кэш, если указан ключ
    if (cacheKey) {
      cache[cacheKey] = data;
      console.log(`Данные сохранены в кэш: ${cacheKey}`);
    }

    return data;
  } catch (error) {
    console.error(`Ошибка при запросе к API (${url}):`, error);
    throw error;
  }
}

/**
 * Сервис для работы с API
 */
const ApiService = {
  /**
   * Получить список всех продуктов
   * @returns {Promise<Array>} - Список продуктов
   */
  getProducts: async (): Promise<any[]> => {
    // Если у нас уже есть кэшированные продукты, возвращаем их
    if (cache.products) {
      return cache.products;
    }

    try {
      // Получаем все продукты без фильтрации
      const products = await fetchApi(
        `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.PRODUCTS}`,
        {},
        "products"
      );

      return products;
    } catch (error) {
      console.error("Ошибка при получении продуктов:", error);
      throw error;
    }
  },

  /**
   * Получить продукты с пагинацией и фильтрацией
   * @param {number} page - Номер страницы (начинается с 1)
   * @param {number} pageSize - Размер страницы
   * @param {string} code - Код для фильтрации (опционально)
   * @param {string} query - Поисковый запрос (опционально)
   * @returns {Promise<Object>} - Объект с продуктами и информацией о пагинации
   */
  getProductsWithPagination: async (
    page: number = 1,
    pageSize: number = 20,
    code?: string,
    query?: string
  ): Promise<any> => {
    try {
      // Вычисляем offset для API (offset начинается с 0)
      const offset = (page - 1) * pageSize;

      // Формируем URL с параметрами пагинации и фильтрации
      const params = new URLSearchParams({
        offset: offset.toString(),
        limit: pageSize.toString(),
      });

      // Добавляем параметры фильтрации, если они есть
      if (code) {
        params.append("code", code);
      }
      if (query) {
        params.append("name", query);
      }

      const url = `${API_CONFIG.BASE_URL}${
        API_CONFIG.ENDPOINTS.PRODUCTS
      }/?${params.toString()}`;

      console.log(`Запрос к API с пагинацией и фильтрами: ${url}`);

      // Получаем заголовки авторизации
      const authHeaders = await authService.getAuthHeaders();

      // Объединяем заголовки
      const headers = {
        ...API_CONFIG.REQUEST_OPTIONS.headers,
        ...authHeaders,
      };

      const response = await fetch(url, {
        ...API_CONFIG.REQUEST_OPTIONS,
        headers,
      });

      if (!response.ok) {
        // Если ошибка авторизации, очищаем токен
        if (response.status === 401 && authService.isAuthRequired()) {
          console.warn("Ошибка авторизации, очищаем токен");
          authService.clearToken();
        }
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();

      // API возвращает просто массив продуктов
      let products: any[], totalItems: number, totalPages: number;

      if (Array.isArray(data)) {
        products = data;

        // Поскольку API не предоставляет общее количество элементов,
        // используем эвристику для определения наличия следующих страниц
        if (data.length === pageSize) {
          // Если получили полную страницу, вероятно есть еще данные
          // Устанавливаем totalPages как минимум на текущую страницу + 1
          totalPages = page + 1;
          totalItems = page * pageSize + data.length; // Показываем реальное количество + предполагаемые
        } else {
          // Если получили неполную страницу, это последняя страница
          totalPages = page;
          totalItems = (page - 1) * pageSize + data.length;
        }
      } else if (
        (data as any).results &&
        Array.isArray((data as any).results)
      ) {
        // Если API возвращает объект с полем results (стандарт Django REST)
        products = (data as any).results;
        totalItems =
          (data as any).count || (data as any).total || products.length;
        totalPages = Math.ceil(totalItems / pageSize);
      } else if ((data as any).data && Array.isArray((data as any).data)) {
        // Если API возвращает объект с полем data
        products = (data as any).data;
        totalItems =
          (data as any).count || (data as any).total || products.length;
        totalPages = Math.ceil(totalItems / pageSize);
      } else {
        // Если структура неизвестна, пробуем использовать как есть
        products = data;
        totalItems = Array.isArray(data) ? data.length : 0;
        totalPages = 1;
      }

      // Формируем результат
      const result = {
        data: products,
        pagination: {
          currentPage: page,
          pageSize: pageSize,
          totalItems,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
        },
        filters: {
          code,
          query,
        },
      };

      console.log(
        `Получено ${result.data.length} продуктов для страницы ${page}`
      );
      return result;
    } catch (error) {
      console.error("Ошибка при получении продуктов с пагинацией:", error);
      throw error;
    }
  },

  /**
   * Получить информацию о продукте по ID
   * @param {string} productId - ID продукта
   * @returns {Promise<Object>} - Информация о продукте
   */
  getProductById: async (productId: string): Promise<any> => {
    const cacheKey = `product_${productId}`;

    try {
      const product = await fetchApi(
        `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.PRODUCT_DETAIL}/${productId}`,
        {},
        cacheKey
      );

      return product;
    } catch (error) {
      console.error(`Ошибка при получении продукта ${productId}:`, error);
      throw error;
    }
  },

  /**
   * Поиск продуктов
   * @param {string} query - Поисковый запрос
   * @returns {Promise<Array>} - Результаты поиска
   */
  searchProducts: async (query: string): Promise<any[]> => {
    if (!query.trim()) {
      return [];
    }

    try {
      const params = new URLSearchParams({
        name: query.trim(),
      });

      const url = `${API_CONFIG.BASE_URL}${
        API_CONFIG.ENDPOINTS.PRODUCTS
      }/?${params.toString()}`;

      const results = await fetchApi(url);
      return results || [];
    } catch (error) {
      console.error("Ошибка при поиске продуктов:", error);
      throw error;
    }
  },

  /**
   * Поиск продуктов с пагинацией
   * @param {string} query - Поисковый запрос
   * @param {number} page - Номер страницы
   * @param {number} pageSize - Размер страницы
   * @returns {Promise<Object>} - Результаты поиска с пагинацией
   */
  searchProductsWithPagination: async (
    query: string,
    page: number = 1,
    pageSize: number = 20
  ): Promise<any> => {
    return await ApiService.getProductsWithPagination(
      page,
      pageSize,
      undefined,
      query
    );
  },

  /**
   * Получить структуру каталога
   * @returns {Promise<Array>} - Структура каталога
   */
  getCatalog: async (): Promise<any[]> => {
    try {
      const catalog = await fetchApi(
        `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CATALOG}`,
        {},
        "catalog"
      );

      return catalog || [];
    } catch (error) {
      console.error("Ошибка при получении каталога:", error);
      throw error;
    }
  },

  /**
   * Получить информацию о текущей конфигурации API
   * @returns {Object} - Информация о конфигурации API
   */
  getApiInfo: () => {
    const authStatus = authService.getAuthStatus();

    return {
      apiType: API_CONFIG.USE_PRODUCTION_API ? "production" : "test",
      baseUrl: API_CONFIG.BASE_URL,
      imagesBaseUrl: API_CONFIG.IMAGES_BASE_URL,
      authRequired: API_CONFIG.AUTH_REQUIRED,
      authStatus,
      endpoints: API_CONFIG.ENDPOINTS,
    };
  },

  /**
   * Очистить кэш
   */
  clearCache: () => {
    Object.keys(cache).forEach((key) => delete cache[key]);
    console.log("Кэш очищен");
  },
};

export default ApiService;
