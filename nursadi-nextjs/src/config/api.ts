// Переключатель API: false - тестовый API, true - основной API
const USE_PRODUCTION_API = false;

// Конфигурация для тестового API
const TEST_API_CONFIG = {
  BASE_URL: "http://test.api.sadi.kz",
  IMAGES_BASE_URL: "https://test.sadi.kz",
  TOKEN_URL: null, // Тестовый API не требует токена
  AUTH_REQUIRED: false,
};

// Конфигурация для основного API
const PRODUCTION_API_CONFIG = {
  BASE_URL: "https://api.sadi.kz",
  IMAGES_BASE_URL: "https://sadi.kz",
  TOKEN_URL: "https://api.sadi.kz/Token",
  AUTH_REQUIRED: true,
  AUTH_CREDENTIALS: {
    grant_type: "password",
    username: "<EMAIL>",
    password: "310312",
  },
  TOKEN_LIFETIME_DAYS: 14,
};

// Выбираем конфигурацию в зависимости от переключателя
const API_CONFIG = USE_PRODUCTION_API ? PRODUCTION_API_CONFIG : TEST_API_CONFIG;

// Добавляем общие настройки
const EXTENDED_API_CONFIG = {
  ...API_CONFIG,
  USE_PRODUCTION_API,

  // Эндпоинты API
  ENDPOINTS: {
    PRODUCTS: "/api/Materials",
    CATEGORIES: "/api/Categories",
    SEARCH: "/api/Materials/search",
    PRODUCT_DETAIL: "/api/Materials",
    CATALOG: "/api/MaterialTrees",
  },

  // Настройки запросов
  REQUEST_OPTIONS: {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
  },

  // Настройки кэширования
  CACHE_SETTINGS: {
    DEFAULT_STALE_TIME: 30 * 60 * 1000, // 30 минут
    DEFAULT_CACHE_TIME: 60 * 60 * 1000, // 1 час
  },
};

export default EXTENDED_API_CONFIG;
