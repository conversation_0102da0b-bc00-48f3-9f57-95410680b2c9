'use client';

import React, { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { CartProvider } from '@/context/CartContext';

export default function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        // Данные считаются устаревшими через 30 минут
        staleTime: 30 * 60 * 1000,
        // Повторять запрос при ошибке до 3 раз
        retry: 3,
        // Кэшировать данные в течение 1 часа
        gcTime: 60 * 60 * 1000, // заменил cacheTime на gcTime (новая версия)
        // Не обновлять данные автоматически при возвращении на страницу
        refetchOnWindowFocus: false,
        // Не обновлять данные автоматически при восстановлении соединения
        refetchOnReconnect: false,
        // Не обновлять данные при повторном монтировании компонента
        refetchOnMount: false,
      },
    },
  }));

  return (
    <QueryClientProvider client={queryClient}>
      <CartProvider>
        {children}
        <ReactQueryDevtools initialIsOpen={false} />
      </CartProvider>
    </QueryClientProvider>
  );
}
