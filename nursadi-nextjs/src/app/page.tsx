import { Metadata } from "next";
import ApiService from "@/services/api.service";
import PriceListClient from "@/components/PriceListClient";

export const metadata: Metadata = {
  title: "SADI.KZ - Прайс-лист строительных материалов",
  description:
    "Актуальные цены на строительные материалы, инструменты и оборудование в Казахстане",
  keywords: "прайс-лист, цены, строительные материалы, SADI",
};

// Настройка ревалидации страницы каждый час
export const revalidate = 3600;

export default async function HomePage() {
  try {
    // Получаем начальные данные на сервере для SSR
    const initialData = await ApiService.getProductsWithPagination(1, 20);

    return <PriceListClient initialData={initialData} initialPage={1} />;
  } catch (error) {
    console.error("Ошибка при загрузке данных на сервере:", error);

    // В случае ошибки возвращаем компонент без начальных данных
    return <PriceListClient initialData={null} initialPage={1} />;
  }
}
