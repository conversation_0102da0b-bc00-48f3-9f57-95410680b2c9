import type { Metadata } from "next";
import "./globals.css";
import Providers from "@/providers/Providers";

export const metadata: Metadata = {
  title: "SADI.KZ - Строительные материалы",
  description: "Строительные материалы инструменты оборудование",
  keywords:
    "строительные материалы, инструменты, оборудование, SADI, Казахстан",
  authors: [{ name: "SADI.KZ" }],
  openGraph: {
    title: "SADI.KZ - Строительные материалы",
    description: "Строительные материалы инструменты оборудование",
    type: "website",
    locale: "ru_RU",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ru">
      <body>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
