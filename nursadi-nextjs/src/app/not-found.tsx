import Link from 'next/link';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Страница не найдена - SADI.KZ',
  description: 'Запрашиваемая страница не найдена',
  robots: 'noindex, nofollow',
};

export default function NotFound() {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      padding: '20px',
      textAlign: 'center',
      background: '#f9fafb'
    }}>
      <div style={{
        background: 'white',
        padding: '48px 32px',
        borderRadius: '8px',
        border: '1px solid #e5e7eb',
        maxWidth: '500px',
        width: '100%'
      }}>
        <h1 style={{
          fontSize: '72px',
          fontWeight: '700',
          color: '#3b82f6',
          margin: '0 0 16px 0'
        }}>
          404
        </h1>
        
        <h2 style={{
          fontSize: '24px',
          fontWeight: '600',
          color: '#111827',
          margin: '0 0 16px 0'
        }}>
          Страница не найдена
        </h2>
        
        <p style={{
          fontSize: '16px',
          color: '#6b7280',
          margin: '0 0 32px 0',
          lineHeight: '1.6'
        }}>
          К сожалению, запрашиваемая страница не существует или была перемещена.
        </p>
        
        <Link
          href="/"
          style={{
            display: 'inline-block',
            padding: '12px 24px',
            background: '#3b82f6',
            color: 'white',
            textDecoration: 'none',
            borderRadius: '6px',
            fontWeight: '600',
            transition: 'background-color 0.2s'
          }}
        >
          Вернуться на главную
        </Link>
      </div>
    </div>
  );
}
