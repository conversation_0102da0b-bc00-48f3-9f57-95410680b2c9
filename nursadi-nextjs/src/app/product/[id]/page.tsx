import { Metadata } from "next";
import { notFound } from "next/navigation";
import ApiService from "@/services/api.service";
import ProductDetailClient from "@/components/ProductDetailClient";

interface ProductPageProps {
  params: {
    id: string;
  };
}

// Генерация метаданных для SEO
export async function generateMetadata({
  params,
}: ProductPageProps): Promise<Metadata> {
  try {
    const product = await ApiService.getProductById(params.id);

    if (!product) {
      return {
        title: "Товар не найден - SADI.KZ",
        description: "Запрашиваемый товар не найден",
      };
    }

    const productName = product.MaterialName || product.name || "Товар";
    const price = product.RetailPrice || product.retailPrice;
    const priceText = price
      ? `Цена от ${new Intl.NumberFormat("ru-RU").format(price)} ₸`
      : "";

    return {
      title: `${productName} - SADI.KZ`,
      description: `Купить ${productName}. ${priceText}. Строительные материалы в Казахстане.`,
      keywords: `${productName}, строительные материалы, купить, цена, SADI`,
      openGraph: {
        title: productName,
        description: `${priceText}. Купить в SADI.KZ`,
        type: "website",
        images: product.imageUrl ? [product.imageUrl] : [],
      },
      alternates: {
        canonical: `/product/${params.id}`,
      },
    };
  } catch (error) {
    console.error("Ошибка при генерации метаданных:", error);
    return {
      title: "Товар - SADI.KZ",
      description: "Информация о товаре",
    };
  }
}

// Настройка ревалидации страницы каждые 2 часа
export const revalidate = 7200;

export default async function ProductPage({ params }: ProductPageProps) {
  try {
    // Получаем данные о товаре на сервере для SSR
    const product = await ApiService.getProductById(params.id);

    if (!product) {
      notFound();
    }

    return <ProductDetailClient product={product} />;
  } catch (error) {
    console.error("Ошибка при загрузке товара:", error);
    notFound();
  }
}
