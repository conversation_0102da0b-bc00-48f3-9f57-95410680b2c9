import { MetadataRoute } from 'next'
import ApiService from '@/services/api.service'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://sadi.kz'
  
  // Статические страницы
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/auth`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
    {
      url: `${baseUrl}/cart`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.3,
    },
  ]

  // Генерируем страницы товаров
  const productPages = []
  
  try {
    // Определяем общее количество страниц
    // Пробуем получить данные с большим offset чтобы найти границу
    let totalPages = 5878 // Используем известное количество
    
    // Генерируем sitemap для всех страниц товаров
    for (let page = 1; page <= totalPages; page++) {
      productPages.push({
        url: `${baseUrl}/products/page/${page}`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: page <= 100 ? 0.9 : page <= 500 ? 0.8 : 0.7, // Приоритет выше для первых страниц
      })
    }
    
    console.log(`Сгенерирован sitemap для ${totalPages} страниц товаров`)
  } catch (error) {
    console.error('Ошибка при генерации sitemap:', error)
    
    // Fallback: генерируем хотя бы первые 1000 страниц
    for (let page = 1; page <= 1000; page++) {
      productPages.push({
        url: `${baseUrl}/products/page/${page}`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: page <= 100 ? 0.9 : 0.8,
      })
    }
  }

  return [...staticPages, ...productPages]
}
