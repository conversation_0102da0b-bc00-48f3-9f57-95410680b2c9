import React from "react";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import ApiService from "@/services/api.service";
import PriceListClient from "@/components/PriceListClient";

interface PageProps {
  params: {
    page: string;
  };
}

// Генерируем статические параметры для всех страниц
export async function generateStaticParams() {
  try {
    // Поскольку API не предоставляет общее количество элементов,
    // мы будем генерировать страницы по мере необходимости
    // Увеличиваем до 500 страниц для лучшего SEO покрытия
    const maxPages = 500;

    const params: { page: string }[] = [];
    for (let i = 1; i <= maxPages; i++) {
      params.push({ page: i.toString() });
    }

    console.log(`Генерируем ${params.length} статических страниц товаров`);
    return params;
  } catch (error) {
    console.error("Ошибка при генерации статических параметров:", error);
    return [{ page: "1" }];
  }
}

// Генерируем метаданные для каждой страницы
export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const pageNumber = parseInt(params.page);

  if (isNaN(pageNumber) || pageNumber < 1) {
    return {
      title: "Страница не найдена | SADI.KZ",
    };
  }

  try {
    // Получаем данные для конкретной страницы
    const data = await ApiService.getProductsWithPagination(pageNumber, 20);

    if (!data?.data?.length) {
      return {
        title: "Страница не найдена | SADI.KZ",
      };
    }

    // Создаем список названий товаров для SEO
    const productNames = data.data
      .map(
        (product: { MaterialName?: string; name?: string }) =>
          product.MaterialName || product.name
      )
      .filter(Boolean)
      .slice(0, 10) // Берем первые 10 для мета-описания
      .join(", ");

    const title = `Строительные материалы - Страница ${pageNumber} | SADI.KZ`;
    const description = `Каталог строительных материалов, страница ${pageNumber}. ${productNames}. Лучшие цены от поставщиков Казахстана.`;

    return {
      title,
      description,
      keywords: `строительные материалы, ${productNames}, поставщики, Казахстан, SADI`,
      openGraph: {
        title,
        description,
        type: "website",
        url: `https://sadi.kz/products/page/${pageNumber}`,
      },
      alternates: {
        canonical: `https://sadi.kz/products/page/${pageNumber}`,
      },
    };
  } catch (error) {
    console.error("Ошибка при генерации метаданных:", error);
    return {
      title: `Строительные материалы - Страница ${pageNumber} | SADI.KZ`,
      description: "Каталог строительных материалов от поставщиков Казахстана",
    };
  }
}

// Включаем ISR с ревалидацией каждые 24 часа
export const revalidate = 86400; // 24 часа в секундах

// Включаем динамические параметры для страниц > 500
export const dynamicParams = true;

export default async function ProductsPage({ params }: PageProps) {
  const pageNumber = parseInt(params.page);

  // Валидация номера страницы
  if (isNaN(pageNumber) || pageNumber < 1) {
    notFound();
  }

  // Проверяем разумные лимиты (максимум ~6000 страниц)
  if (pageNumber > 6000) {
    notFound();
  }

  try {
    // Получаем данные для страницы на сервере (для SSG/ISR)
    const initialData = await ApiService.getProductsWithPagination(
      pageNumber,
      20
    );

    if (!initialData?.data?.length && pageNumber > 1) {
      notFound();
    }

    // Передаем начальные данные и номер страницы в клиентский компонент
    return (
      <PriceListClient initialData={initialData} initialPage={pageNumber} />
    );
  } catch (error) {
    console.error("Ошибка при загрузке страницы товаров:", error);
    notFound();
  }
}
