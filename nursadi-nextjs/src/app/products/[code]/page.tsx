import { Metadata } from "next";
import { notFound } from "next/navigation";
import ApiService from "@/services/api.service";
import ProductDetailClient from "@/components/ProductDetailClient";

interface ProductPageProps {
  params: {
    code: string;
  };
}

// Генерация статических путей для популярных товаров
export async function generateStaticPaths() {
  try {
    // Получаем первые 100 товаров для предварительной генерации
    const response = await ApiService.getProductsWithPagination(1, 100);
    const products = response.data || [];

    const paths = products
      .filter((product: any) => product.MaterialCode || product.code)
      .map((product: any) => ({
        params: {
          code: (product.MaterialCode || product.code || product.id).toString(),
        },
      }))
      .slice(0, 50); // Ограничиваем до 50 для быстрого билда

    return {
      paths,
      fallback: "blocking", // ISR для остальных товаров
    };
  } catch (error) {
    console.error("Ошибка при генерации путей товаров:", error);
    return {
      paths: [],
      fallback: "blocking",
    };
  }
}

// Генерация метаданных для SEO
export async function generateMetadata({
  params,
}: ProductPageProps): Promise<Metadata> {
  try {
    const product = await ApiService.getProductById(params.code);

    if (!product) {
      return {
        title: "Товар не найден",
        description: "Запрашиваемый товар не найден в каталоге SADI.KZ",
      };
    }

    const productName = product.MaterialName || product.name || "Товар";
    const description = product.Description || 
      `${productName}. Купить строительные материалы в SADI.KZ. Лучшие цены от проверенных поставщиков.`;
    
    const priceText = product.RetailPrice 
      ? `от ${new Intl.NumberFormat("ru-RU").format(product.RetailPrice)} ₸`
      : "Цена по запросу";

    return {
      title: `${productName} - ${priceText} | SADI.KZ`,
      description: description.slice(0, 160),
      keywords: [
        productName,
        "строительные материалы",
        "купить",
        "SADI.KZ",
        "поставщики",
        "оптом",
        "розница"
      ].join(", "),
      openGraph: {
        title: `${productName} - ${priceText}`,
        description: description.slice(0, 160),
        type: "website",
        images: product.imageUrl ? [
          {
            url: product.imageUrl,
            width: 800,
            height: 600,
            alt: productName,
          }
        ] : [],
        siteName: "SADI.KZ",
      },
      twitter: {
        card: "summary_large_image",
        title: `${productName} - ${priceText}`,
        description: description.slice(0, 160),
        images: product.imageUrl ? [product.imageUrl] : [],
      },
      alternates: {
        canonical: `https://sadi.kz/products/${params.code}`,
      },
      other: {
        "product:price:amount": product.RetailPrice?.toString() || "",
        "product:price:currency": "KZT",
        "product:availability": "in stock",
      },
    };
  } catch (error) {
    console.error("Ошибка при генерации метаданных:", error);
    return {
      title: "Товар | SADI.KZ",
      description: "Строительные материалы и оборудование в SADI.KZ",
    };
  }
}

export default async function ProductPage({ params }: ProductPageProps) {
  try {
    const product = await ApiService.getProductById(params.code);

    if (!product) {
      notFound();
    }

    // JSON-LD Schema.org разметка для товара
    const jsonLd = {
      "@context": "https://schema.org",
      "@type": "Product",
      name: product.MaterialName || product.name,
      description: product.Description || `${product.MaterialName || product.name} - строительные материалы`,
      image: product.imageUrl || "/images/placeholder.png",
      brand: {
        "@type": "Brand",
        name: "SADI.KZ",
      },
      offers: {
        "@type": "AggregateOffer",
        priceCurrency: "KZT",
        lowPrice: product.WholesalePrice || product.RetailPrice || 0,
        highPrice: product.RetailPrice || product.WholesalePrice || 0,
        availability: "https://schema.org/InStock",
        seller: {
          "@type": "Organization",
          name: "SADI.KZ",
        },
      },
      aggregateRating: product.rating ? {
        "@type": "AggregateRating",
        ratingValue: product.rating,
        reviewCount: product.reviewCount || 1,
      } : undefined,
    };

    return (
      <>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
        <ProductDetailClient product={product} />
      </>
    );
  } catch (error) {
    console.error("Ошибка при загрузке товара:", error);
    notFound();
  }
}

// ISR - обновление каждые 24 часа
export const revalidate = 86400;
