"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import Layout from "./Layout";
import SimpleSearchWithCatalog from "./SimpleSearchWithCatalog";
import ProductCard from "./ProductCard";
import SkeletonCard from "./SkeletonCard";
import Pagination from "./Pagination";
import { useQueryClient } from "@tanstack/react-query";
import ApiService from "@/services/api.service";
import {
  usePaginatedProducts,
  usePaginatedFilteredProducts,
  usePaginatedSearchProducts,
  usePaginatedSearchWithCategory,
} from "@/hooks/useApi";

const PriceListContainer = styled.div`
  padding: 24px;
  flex-grow: 1;

  @media (max-width: 768px) {
    padding: 16px;
  }
`;

const Title = styled.h1`
  font-size: 42px;
  line-height: 150%;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 22px;
    margin-bottom: 12px;
    display: none; /* Скрываем заголовок на мобильных, так как он уже есть в шапке */
  }
`;

const Description = styled.p`
  font-size: 17px;
  max-width: 656px;
  color: #666;
  margin-bottom: 24px;
  line-height: 150%;

  a {
    color: #0066cc;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  @media (max-width: 768px) {
    font-size: 13px;
    margin-bottom: 16px;
  }
`;

const ProductGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  transition: opacity 0.3s ease-in-out;

  /* Все карточки в строке будут одинаковой высоты */
  & > * {
    height: 100%;
    animation: fadeInUp 0.4s ease-out;
    animation-fill-mode: both;
  }

  /* Анимация появления карточек */
  & > *:nth-child(1) {
    animation-delay: 0.05s;
  }
  & > *:nth-child(2) {
    animation-delay: 0.1s;
  }
  & > *:nth-child(3) {
    animation-delay: 0.15s;
  }
  & > *:nth-child(4) {
    animation-delay: 0.2s;
  }
  & > *:nth-child(5) {
    animation-delay: 0.25s;
  }
  & > *:nth-child(6) {
    animation-delay: 0.3s;
  }
  & > *:nth-child(7) {
    animation-delay: 0.35s;
  }
  & > *:nth-child(8) {
    animation-delay: 0.4s;
  }
  & > *:nth-child(9) {
    animation-delay: 0.45s;
  }
  & > *:nth-child(10) {
    animation-delay: 0.5s;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`;

const LoadingGrid = styled(ProductGrid)``;

const ActiveFilters = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
  margin-bottom: 16px;
  min-height: 32px; /* Резервируем минимальную высоту для предотвращения сдвига */
  align-items: flex-start;
`;

const FilterTag = styled.div`
  background-color: #f0f7ff;
  border: 1px solid #cce4ff;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  color: #0066cc;
  display: flex;
  align-items: center;
  gap: 8px;

  button {
    background: none;
    border: none;
    color: #0066cc;
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    display: flex;
    align-items: center;
  }
`;

const ErrorMessage = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: #ef4444;
  font-size: 16px;
`;

const NoResultsMessage = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  font-size: 16px;
`;

interface PriceListClientProps {
  initialData: any;
}

const PriceListClient: React.FC<PriceListClientProps> = ({ initialData }) => {
  const router = useRouter();
  const queryClient = useQueryClient();

  // Состояние для поиска и фильтрации
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);

  // Хуки для разных типов запросов с правильными условиями enabled
  const defaultQuery = usePaginatedProducts(currentPage, pageSize, {
    initialData:
      currentPage === 1 && !searchQuery && !selectedCategory
        ? initialData
        : undefined,
    enabled: !searchQuery && !selectedCategory,
  });

  const categoryQuery = usePaginatedFilteredProducts(
    selectedCategory,
    currentPage,
    pageSize,
    {
      enabled: !!selectedCategory && !searchQuery,
    }
  );

  const searchQuery_hook = usePaginatedSearchProducts(
    searchQuery,
    currentPage,
    pageSize,
    {
      enabled: !!searchQuery && !selectedCategory,
    }
  );

  const searchWithCategoryQuery = usePaginatedSearchWithCategory(
    searchQuery,
    selectedCategory,
    currentPage,
    pageSize,
    {
      enabled: !!searchQuery && !!selectedCategory,
    }
  );

  // Выбираем активный запрос
  const activeQuery =
    searchQuery && selectedCategory
      ? searchWithCategoryQuery
      : searchQuery
      ? searchQuery_hook
      : selectedCategory
      ? categoryQuery
      : defaultQuery;

  const { data, isLoading, error, isFetching } = activeQuery;

  // Обработчики событий
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Сбрасываем на первую страницу при новом поиске
  };

  const handleCategoryChange = (category: any) => {
    setSelectedCategory(category);
    setCurrentPage(1); // Сбрасываем на первую страницу при смене категории
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Прокручиваем к началу страницы
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handleProductClick = (productId: string) => {
    router.push(`/product/${productId}`);
  };

  const handleClearFilter = () => {
    setSelectedCategory(null);
    setCurrentPage(1);
  };

  const handleClearSearch = () => {
    setSearchQuery("");
    setCurrentPage(1);
  };

  // Предзагрузка данных для следующей страницы
  useEffect(() => {
    if (data?.pagination?.hasNextPage) {
      const nextPage = currentPage + 1;

      // Предзагружаем данные для следующей страницы
      if (searchQuery && selectedCategory) {
        queryClient.prefetchQuery({
          queryKey: [
            "paginatedSearchWithCategory",
            searchQuery,
            selectedCategory?.department || "",
            selectedCategory?.section || "",
            selectedCategory?.subsection || "",
            selectedCategory?.group || "",
            nextPage,
            pageSize,
          ],
          queryFn: () =>
            ApiService.getProductsWithPagination(
              nextPage,
              pageSize,
              selectedCategory?.code,
              searchQuery
            ),
        });
      } else if (searchQuery) {
        queryClient.prefetchQuery({
          queryKey: [
            "paginatedSearchProducts",
            searchQuery,
            nextPage,
            pageSize,
          ],
          queryFn: () =>
            ApiService.searchProductsWithPagination(
              searchQuery,
              nextPage,
              pageSize
            ),
        });
      } else if (selectedCategory) {
        queryClient.prefetchQuery({
          queryKey: [
            "paginatedFilteredProducts",
            selectedCategory?.department || "",
            selectedCategory?.section || "",
            selectedCategory?.subsection || "",
            selectedCategory?.group || "",
            nextPage,
            pageSize,
          ],
          queryFn: () =>
            ApiService.getProductsWithPagination(
              nextPage,
              pageSize,
              selectedCategory?.code
            ),
        });
      } else {
        queryClient.prefetchQuery({
          queryKey: ["paginatedProducts", nextPage, pageSize],
          queryFn: () =>
            ApiService.getProductsWithPagination(nextPage, pageSize),
        });
      }
    }
  }, [data, currentPage, pageSize, searchQuery, selectedCategory, queryClient]);

  return (
    <Layout>
      <PriceListContainer>
        <Title>Строительные материалы инструменты оборудование</Title>
        <Description>
          Здесь Вы можете находить ценовые предложения наших партнеров. Если Вы
          являетесь поставщиком, то{" "}
          <a href="#" style={{ color: "#0066cc" }}>
            добавьте ценовое предложение
          </a>
          , это бесплатно.
        </Description>

        <ActiveFilters role="status" aria-live="polite">
          {selectedCategory && (
            <FilterTag>
              {selectedCategory.groupName
                ? `Группа: ${selectedCategory.groupName}`
                : selectedCategory.subsectionName
                ? `Подраздел: ${selectedCategory.subsectionName}`
                : selectedCategory.sectionName
                ? `Раздел: ${selectedCategory.sectionName}`
                : `Отдел: ${selectedCategory.departmentName}`}
              <button
                onClick={handleClearFilter}
                aria-label="Очистить фильтр категории"
              >
                ×
              </button>
            </FilterTag>
          )}

          {searchQuery && (
            <FilterTag>
              Поиск: {searchQuery}
              <button
                onClick={handleClearSearch}
                aria-label="Очистить поисковый запрос"
              >
                ×
              </button>
            </FilterTag>
          )}
        </ActiveFilters>

        <SimpleSearchWithCatalog
          onSearch={handleSearch}
          onCategoryChange={handleCategoryChange}
        />

        {error && (
          <ErrorMessage>
            Произошла ошибка при загрузке данных. Попробуйте обновить страницу.
          </ErrorMessage>
        )}

        {isLoading ? (
          <LoadingGrid>
            {Array.from({ length: pageSize }, (_, index) => (
              <SkeletonCard key={index} />
            ))}
          </LoadingGrid>
        ) : data?.data?.length > 0 ? (
          <>
            <ProductGrid>
              {data.data.map((product: any) => (
                <ProductCard
                  key={product.id || product.MaterialId}
                  product={product}
                  onClick={() =>
                    handleProductClick(product.id || product.MaterialId)
                  }
                />
              ))}
            </ProductGrid>

            {data.pagination && (
              <Pagination
                currentPage={data.pagination.currentPage}
                totalPages={data.pagination.totalPages}
                totalItems={data.pagination.totalItems}
                pageSize={pageSize}
                onPageChange={handlePageChange}
              />
            )}
          </>
        ) : (
          <NoResultsMessage>
            {searchQuery || selectedCategory
              ? "По вашему запросу ничего не найдено. Попробуйте изменить критерии поиска."
              : "Товары не найдены."}
          </NoResultsMessage>
        )}
      </PriceListContainer>
    </Layout>
  );
};

export default PriceListClient;
