"use client";

import React, { useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import styled from "styled-components";

const Overlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: ${(props) => (props.$isOpen ? 1 : 0)};
  visibility: ${(props) => (props.$isOpen ? "visible" : "hidden")};
  transition: all 0.3s ease;

  @media (min-width: 769px) {
    display: none;
  }
`;
Overlay.displayName = "Overlay";

const MenuContainer = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  z-index: 1000;
  display: ${(props) => (props.$isOpen ? "flex" : "none")};
  flex-direction: column;
  overflow-y: auto;
`;
MenuContainer.displayName = "MenuContainer";

const MenuHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #eee;
`;
MenuHeader.displayName = "MenuHeader";

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
`;
CloseButton.displayName = "CloseButton";

const MenuTitle = styled.h2`
  font-size: 18px;
  font-weight: 500;
  margin: 0;
`;
MenuTitle.displayName = "MenuTitle";

const MenuList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;
MenuList.displayName = "MenuList";

const MenuItem = styled.li`
  border-bottom: 1px solid #eee;
`;
MenuItem.displayName = "MenuItem";

const MenuLink = styled.div<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  color: #333;
  text-decoration: none;
  font-size: 16px;
  cursor: pointer;

  svg {
    width: 16px;
    height: 16px;
  }

  ${(props) =>
    props.$active &&
    `
    background-color: #f0f7ff;
    color: #0066cc;
    font-weight: 500;
  `}
`;
MenuLink.displayName = "MenuLink";

const SubMenuContainer = styled.div<{ $isOpen: boolean }>`
  display: ${(props) => (props.$isOpen ? "block" : "none")};
  background-color: #f9f9f9;
`;
SubMenuContainer.displayName = "SubMenuContainer";

const SubMenuItem = styled.li`
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }
`;
SubMenuItem.displayName = "SubMenuItem";

const SubMenuLink = styled.div<{ $active?: boolean }>`
  display: block;
  padding: 14px 16px 14px 32px;
  color: #333;
  text-decoration: none;
  font-size: 14px;
  cursor: pointer;

  ${(props) =>
    props.$active &&
    `
    background-color: #e6f0ff;
    color: #0066cc;
    font-weight: 500;
    border-left: 3px solid #0066cc;
    padding-left: 29px;
  `}
`;
SubMenuLink.displayName = "SubMenuLink";

const MenuIcon = styled.img`
  width: 16px;
  height: 16px;
  margin-right: 12px;
`;
MenuIcon.displayName = "MenuIcon";

const Logo = styled.div`
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
  }

  img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 10px;
  }

  span {
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }
`;
Logo.displayName = "Logo";

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileMenu: React.FC<MobileMenuProps> = ({ isOpen, onClose }) => {
  const router = useRouter();
  const pathname = usePathname();
  const [openSubMenu, setOpenSubMenu] = useState(true);
  const [activeItem, setActiveItem] = useState("Прайс листы");

  // Обновляем активный элемент на основе текущего пути
  React.useEffect(() => {
    if (pathname === "/cart") {
      setActiveItem("Корзина");
    } else if (pathname === "/auth") {
      setActiveItem("Авторизация");
    } else if (pathname === "/create-tender") {
      setActiveItem("Создать тендер (подменю)");
    } else if (pathname === "/" || pathname === "/price-list") {
      setActiveItem("Прайс листы");
    }
  }, [pathname]);

  const toggleSubMenu = () => {
    setOpenSubMenu(!openSubMenu);
  };

  const handleItemClick = (itemName: string, path: string) => {
    setActiveItem(itemName);
    onClose(); // Закрываем меню

    // Если это внешняя ссылка, открываем в новом окне
    if (path.startsWith("http")) {
      window.open(path, "_blank");
    } else {
      router.push(path);
    }
  };

  return (
    <MenuContainer $isOpen={isOpen}>
      <MenuHeader>
        <CloseButton onClick={onClose}>×</CloseButton>
        <MenuTitle>Меню</MenuTitle>
      </MenuHeader>

      <Logo onClick={() => handleItemClick("Авторизация", "/auth")}>
        <img src="/images/null.jpg" alt="Аватар пользователя" />
        <span>Авторизация</span>
      </Logo>

      <MenuList>
        <MenuItem>
          <MenuLink
            $active={activeItem === "Корзина"}
            onClick={() => handleItemClick("Корзина", "/cart")}
          >
            <div style={{ display: "flex", alignItems: "center" }}>
              <MenuIcon src="/icons/mainbusket.svg" alt="иконка" />
              Корзина
            </div>
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 4L10 8L6 12"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </MenuLink>
        </MenuItem>

        <MenuItem>
          <MenuLink $active={false} onClick={toggleSubMenu}>
            <div style={{ display: "flex", alignItems: "center" }}>
              <MenuIcon src="/icons/Icon1.svg" alt="иконка" />
              Тендеры
            </div>
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              style={{
                transform: openSubMenu ? "rotate(90deg)" : "rotate(0deg)",
                transition: "transform 0.2s ease",
              }}
            >
              <path
                d="M6 4L10 8L6 12"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </MenuLink>
          <SubMenuContainer $isOpen={openSubMenu}>
            <ul style={{ listStyle: "none", padding: 0, margin: 0 }}>
              <SubMenuItem>
                <SubMenuLink
                  $active={activeItem === "Создать тендер (подменю)"}
                  onClick={() =>
                    handleItemClick(
                      "Создать тендер (подменю)",
                      "/create-tender"
                    )
                  }
                >
                  Создать тендер
                </SubMenuLink>
              </SubMenuItem>
              <SubMenuItem>
                <SubMenuLink
                  $active={activeItem === "Найти тендер"}
                  onClick={() =>
                    handleItemClick(
                      "Найти тендер",
                      "https://sadi.kz/PurchaseNew/CreateNewTender"
                    )
                  }
                >
                  Найти тендер
                </SubMenuLink>
              </SubMenuItem>
            </ul>
          </SubMenuContainer>
        </MenuItem>

        <MenuItem>
          <MenuLink
            $active={activeItem === "Прайс листы"}
            onClick={() => handleItemClick("Прайс листы", "/")}
          >
            <div style={{ display: "flex", alignItems: "center" }}>
              <MenuIcon src="/icons/Icon3.svg" alt="иконка" />
              Прайс листы
            </div>
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 4L10 8L6 12"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </MenuLink>
        </MenuItem>

        <MenuItem>
          <MenuLink
            $active={activeItem === "Мои тендеры"}
            onClick={() => handleItemClick("Мои тендеры", "#")}
          >
            <div style={{ display: "flex", alignItems: "center" }}>
              <MenuIcon src="/icons/Icon4.svg" alt="иконка" />
              Мои тендеры
            </div>
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 4L10 8L6 12"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </MenuLink>
        </MenuItem>

        <MenuItem>
          <MenuLink
            $active={activeItem === "Мои предложения"}
            onClick={() => handleItemClick("Мои предложения", "#")}
          >
            <div style={{ display: "flex", alignItems: "center" }}>
              <MenuIcon src="/icons/Icon5.svg" alt="иконка" />
              Мои предложения
            </div>
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 4L10 8L6 12"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </MenuLink>
        </MenuItem>

        <MenuItem>
          <MenuLink
            $active={activeItem === "Избранное"}
            onClick={() => handleItemClick("Избранное", "#")}
          >
            <div style={{ display: "flex", alignItems: "center" }}>
              <MenuIcon src="/icons/Icon6.svg" alt="иконка" />
              Избранное
            </div>
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 4L10 8L6 12"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </MenuLink>
        </MenuItem>

        <MenuItem>
          <MenuLink
            $active={activeItem === "Настройки"}
            onClick={() => handleItemClick("Настройки", "#")}
          >
            <div style={{ display: "flex", alignItems: "center" }}>
              <MenuIcon src="/icons/Icon7.svg" alt="иконка" />
              Настройки
            </div>
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 4L10 8L6 12"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </MenuLink>
        </MenuItem>
      </MenuList>
    </MenuContainer>
  );
};

MobileMenu.displayName = "MobileMenu";

export default MobileMenu;
