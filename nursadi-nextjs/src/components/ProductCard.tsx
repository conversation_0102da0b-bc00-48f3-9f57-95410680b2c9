"use client";

import React from "react";
import styled from "styled-components";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useCart } from "@/context/CartContext";

const Card = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;
Card.displayName = "Card";

const ImageContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 160px;
  padding: 12px;
  background-color: transparent;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: opacity 0.3s ease;
  }
`;
ImageContainer.displayName = "ImageContainer";

const Title = styled.h3`
  font-size: 17px;
  font-weight: bold;
  color: #333;
  line-height: 150%;
  margin-top: 16px;
  min-height: 50px;
  overflow: visible;
  display: block;
`;
Title.displayName = "Title";

const PriceInfo = styled.div`
  margin-top: 24px;
  flex: 0 0 auto;
`;
PriceInfo.displayName = "PriceInfo";

const PriceRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 14px;
  color: #808185;
  border-bottom: 1px solid #ddd;
  padding-bottom: 8px;
`;
PriceRow.displayName = "PriceRow";

const Price = styled.span<{ $bold?: boolean }>`
  font-weight: ${(props) => (props.$bold ? "600" : "400")};
`;
Price.displayName = "Price";

const ButtonContainer = styled.div`
  margin-top: auto;
  padding-top: 16px;
  display: flex;
  justify-content: space-between;

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 10px;
  }
`;
ButtonContainer.displayName = "ButtonContainer";

const OrderButton = styled.button`
  padding: 6px 10px;
  background-color: white;
  font-weight: bold;
  color: #333;
  border: 2px solid #d6dce1;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    background-color: #f5f5f5;
  }
`;
OrderButton.displayName = "OrderButton";

const CartButton = styled.button`
  padding: 6px 10px;
  border-radius: 8px;
  font-weight: 600;
  background-color: #0066cc;
  color: white;
  border: none;

  &:hover {
    background-color: #0055b3;
  }
`;
CartButton.displayName = "CartButton";

interface ProductCardProps {
  product: any;
  onClick: () => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onClick }) => {
  const { addToCart } = useCart();
  const router = useRouter();

  const handleCardClick = () => {
    onClick();
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();

    const cartItem = {
      id: product.MaterialId || product.id,
      MaterialName: product.MaterialName || product.name,
      retailPrice: product.RetailPrice ? parseFloat(product.RetailPrice) : null,
      wholesalePrice: product.WholesalePrice
        ? parseFloat(product.WholesalePrice)
        : null,
      image: getImageUrl(),
    };

    addToCart(cartItem);
  };

  const handleOrderNow = (e: React.MouseEvent) => {
    e.stopPropagation();

    const cartItem = {
      id: product.MaterialId || product.id,
      MaterialName: product.MaterialName || product.name,
      retailPrice: product.RetailPrice ? parseFloat(product.RetailPrice) : null,
      wholesalePrice: product.WholesalePrice
        ? parseFloat(product.WholesalePrice)
        : null,
      image: getImageUrl(),
    };

    addToCart(cartItem);
    router.push("/cart");
  };

  const getImageUrl = () => {
    if (product.imageUrl) return product.imageUrl;
    if (product.ImageUrl) return product.ImageUrl;
    if (product.image) return product.image;
    return null;
  };

  const imageUrl = getImageUrl();

  return (
    <Card as="article" onClick={handleCardClick}>
      <ImageContainer>
        <Image
          src={imageUrl || "/images/placeholder.png"}
          alt={product.MaterialName || product.name || "Товар"}
          width={280}
          height={160}
          style={{ objectFit: "contain" }}
          loading="lazy"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = "/images/placeholder.png";
          }}
        />
      </ImageContainer>

      <Title as="h2">
        {product.MaterialName || product.name || "Название не указано"}
      </Title>

      <PriceInfo>
        <PriceRow>
          <span>Сред. розн. цена:</span>
          <Price $bold>{product.RetailPrice || "По запросу"} ₸</Price>
        </PriceRow>
        <PriceRow>
          <span>Сред. опт. цена:</span>
          <Price>{product.WholesalePrice || "По запросу"} ₸</Price>
        </PriceRow>
        <PriceRow>
          <span>Поставщиков</span>
          <Price $bold> {product.SuppliersCount || 0}</Price>
        </PriceRow>
      </PriceInfo>

      <ButtonContainer>
        <OrderButton
          onClick={handleOrderNow}
          aria-label={`Оформить заказ на ${
            product.MaterialName || product.name
          }`}
        >
          Оформить сейчас
        </OrderButton>
        <CartButton
          onClick={handleAddToCart}
          aria-label={`Добавить ${
            product.MaterialName || product.name
          } в корзину`}
        >
          В корзину
        </CartButton>
      </ButtonContainer>
    </Card>
  );
};

ProductCard.displayName = "ProductCard";

export default ProductCard;
