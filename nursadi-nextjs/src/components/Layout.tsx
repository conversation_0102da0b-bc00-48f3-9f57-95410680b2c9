"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import styled from "styled-components";
import Sidebar from "./Sidebar";
import Header from "./Header";
import MobileMenu from "./MobileMenu";
import { useCart } from "@/context/CartContext";

const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
`;
LayoutContainer.displayName = "LayoutContainer";

const MainContent = styled.div`
  flex-grow: 1;
  display: flex;
  flex-direction: column;
`;
MainContent.displayName = "MainContent";

const MobileHeader = styled.div`
  display: none;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 100;

  @media (max-width: 768px) {
    display: flex;
  }
`;
MobileHeader.displayName = "MobileHeader";

const MobileMenuButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f3f4f6;
  }

  svg {
    width: 24px;
    height: 24px;
    color: #374151;
  }
`;
MobileMenuButton.displayName = "MobileMenuButton";

const MobileTitle = styled.h1`
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
`;
MobileTitle.displayName = "MobileTitle";

const MobileCartButton = styled.button`
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f3f4f6;
  }
`;
MobileCartButton.displayName = "MobileCartButton";

const CartBadge = styled.span`
  position: absolute;
  top: 4px;
  right: 4px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 18px;
`;
CartBadge.displayName = "CartBadge";

const DesktopHeader = styled.div`
  display: block;

  @media (max-width: 768px) {
    display: none;
  }
`;
DesktopHeader.displayName = "DesktopHeader";

const StyledLayoutContainer = styled(LayoutContainer)``;
const StyledMainContent = styled(MainContent)``;
const StyledMobileHeader = styled(MobileHeader)``;
const StyledDesktopHeader = styled(DesktopHeader)``;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const router = useRouter();
  const pathname = usePathname();
  const { getCartItemsCount } = useCart();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Закрываем мобильное меню при изменении маршрута
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [pathname]);

  const handleCartClick = () => {
    router.push("/cart");
  };

  const handleMobileMenuToggle = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <StyledLayoutContainer>
      <Sidebar />

      <StyledMainContent>
        <StyledMobileHeader>
          <MobileMenuButton
            onClick={handleMobileMenuToggle}
            aria-label="Открыть меню"
          >
            <svg
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </MobileMenuButton>

          <MobileTitle as="h1">Прайс листы</MobileTitle>

          <MobileCartButton
            onClick={handleCartClick}
            aria-label={`Корзина, ${getCartItemsCount()} товаров`}
          >
            <img
              src="/icons/mainbusket.svg"
              width="24"
              height="24"
              alt="Иконка корзины"
            />
            {getCartItemsCount() > 0 && (
              <CartBadge
                aria-label={`${getCartItemsCount()} товаров в корзине`}
              >
                {getCartItemsCount()}
              </CartBadge>
            )}
          </MobileCartButton>
        </StyledMobileHeader>

        <StyledDesktopHeader as="header" role="banner">
          <Header onCartClick={handleCartClick} />
        </StyledDesktopHeader>

        <main>{children}</main>
      </StyledMainContent>

      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
      />
    </StyledLayoutContainer>
  );
};

Layout.displayName = "Layout";

export default Layout;
