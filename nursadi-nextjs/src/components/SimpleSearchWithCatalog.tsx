"use client";

import React, { useState } from "react";
import styled from "styled-components";
import CatalogHierarchical from "./CatalogHierarchical";

const SearchContainer = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
  }
`;

const SearchInputContainer = styled.div`
  position: relative;
  flex: 1;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 16px;
  border: 1px solid #d5d5d6;
  border-radius: 8px;
  font-size: 17px;

  &:focus {
    outline: none;
    border-color: #0066cc;
  }

  @media (max-width: 768px) {
    padding: 14px;
    font-size: 14px;
  }
`;

const SearchButton = styled.button`
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 52px;
  background-color: #0066cc;
  border: none;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &:hover {
    background: #0052a3;
  }

  svg {
    width: 16px;
    height: 16px;
    color: white;
  }
`;

interface SimpleSearchWithCatalogProps {
  onSearch: (query: string) => void;
  onCategoryChange: (category: any) => void;
}

const SimpleSearchWithCatalog: React.FC<SimpleSearchWithCatalogProps> = ({
  onSearch,
  onCategoryChange,
}) => {
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = () => {
    onSearch(searchQuery);
  };

  const handleCategorySelect = (category: any) => {
    onCategoryChange(category);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <SearchContainer>
      <CatalogHierarchical onCategorySelect={handleCategorySelect} />

      <SearchInputContainer>
        <SearchInput
          type="text"
          placeholder="Поиск по каталогу"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyPress={handleKeyPress}
        />
        <SearchButton onClick={handleSearch}>
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7 13C10.3137 13 13 10.3137 13 7C13 3.68629 10.3137 1 7 1C3.68629 1 1 3.68629 1 7C1 10.3137 3.68629 13 7 13Z"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M15 15L11 11"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </SearchButton>
      </SearchInputContainer>
    </SearchContainer>
  );
};

export default SimpleSearchWithCatalog;
