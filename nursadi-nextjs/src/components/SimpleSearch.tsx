"use client";

import React, { useState } from "react";
import styled from "styled-components";

const SearchContainer = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
  }
`;

const CategorySelect = styled.select`
  padding: 16px;
  border: 1px solid #d5d5d6;
  border-radius: 8px;
  font-size: 17px;
  background: white;
  min-width: 200px;

  &:focus {
    outline: none;
    border-color: #0066cc;
  }

  @media (max-width: 768px) {
    width: 100%;
    min-width: auto;
    font-size: 14px;
  }
`;

const SearchInputContainer = styled.div`
  position: relative;
  flex: 1;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 16px;
  border: 1px solid #d5d5d6;
  border-radius: 8px;
  font-size: 17px;

  &:focus {
    outline: none;
    border-color: #0066cc;
  }

  @media (max-width: 768px) {
    padding: 14px;
    font-size: 14px;
  }
`;

const SearchButton = styled.button`
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 52px;
  background-color: #0066cc;
  border: none;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &:hover {
    background: #0052a3;
  }

  svg {
    width: 16px;
    height: 16px;
    color: white;
  }
`;

interface SimpleSearchProps {
  onSearch: (query: string) => void;
  onCategoryChange: (category: string) => void;
}

const SimpleSearch: React.FC<SimpleSearchProps> = ({
  onSearch,
  onCategoryChange,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");

  const handleSearch = () => {
    onSearch(searchQuery);
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const category = e.target.value;
    setSelectedCategory(category);
    onCategoryChange(category);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <SearchContainer>
      <CategorySelect value={selectedCategory} onChange={handleCategoryChange}>
        <option value="">Каталог</option>
        <option value="building-materials">Строительные материалы</option>
        <option value="tools">Инструменты</option>
        <option value="equipment">Оборудование</option>
        <option value="concrete">Бетон</option>
        <option value="metal">Металлопрокат</option>
        <option value="wood">Пиломатериалы</option>
      </CategorySelect>

      <SearchInputContainer>
        <SearchInput
          type="text"
          placeholder="Поиск по каталогу"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyPress={handleKeyPress}
        />
        <SearchButton onClick={handleSearch}>
          <svg
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </SearchButton>
      </SearchInputContainer>
    </SearchContainer>
  );
};

export default SimpleSearch;
