"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import Layout from "./Layout";
import { useCart } from "@/context/CartContext";

const ProductDetailContainer = styled.div`
  padding: 24px;
  flex-grow: 1;

  @media (max-width: 768px) {
    padding: 16px;
  }
`;

const Breadcrumbs = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  font-size: 14px;
  color: #666;

  a {
    color: #666;
    text-decoration: none;

    &:hover {
      color: #0066cc;
    }
  }

  svg {
    margin: 0 8px;
    width: 16px;
    height: 16px;
  }
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  padding: 0;
  margin-right: 16px;

  &:hover {
    color: #0066cc;
  }

  svg {
    margin-right: 8px;
  }
`;

const Title = styled.h1`
  font-size: 42px;
  font-weight: bold;
  max-width: 656px;
  color: #333;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    font-size: 24px;
    margin-bottom: 16px;
  }
`;

const ProductContent = styled.div`
  display: flex;
  gap: 24px;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
`;

const ProductImages = styled.div`
  flex: 0 0 65%;
  border-radius: 8px;
  display: flex;
  gap: 16px;

  @media (max-width: 768px) {
    flex: none;
    flex-direction: column;
    gap: 12px;
  }
`;

const ThumbnailsColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;

  @media (max-width: 768px) {
    flex-direction: row;
    gap: 8px;
    order: 2;
    overflow-x: auto;
    padding-bottom: 8px;
  }
`;

const Thumbnail = styled.div<{ $active?: boolean }>`
  width: 176px;
  height: 176px;
  border: 1px solid ${(props) => (props.$active ? "#0066cc" : "#e0e0e0")};
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;

  @media (max-width: 768px) {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: white;
  }
`;

const MainImage = styled.div`
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  overflow: hidden;

  @media (max-width: 768px) {
    order: 1;
    height: 250px;
    border: 1px solid #e0e0e0;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: white;
  }
`;

const ProductInfo = styled.div`
  max-width: 272px;
  max-height: 380px;
  background-color: white;
  border-radius: 8px;
  padding: 24px 16.5px;

  @media (max-width: 768px) {
    max-width: none;
    max-height: none;
    padding: 16px;
  }
`;

const ProductInfoTitle = styled.h2`
  font-size: 24px;
  font-weight: bold;
  line-height: 150%;
  color: #333;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 20px;
    margin-bottom: 12px;
  }
`;

const PriceInfo = styled.div`
  margin-bottom: 16px;
`;

const PriceRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 14px;
  color: #808185;
  border-bottom: 1px solid #ddd;
  padding-bottom: 8px;
`;

const Price = styled.span<{ $bold?: boolean }>`
  font-weight: ${(props) => (props.$bold ? "600" : "400")};
`;

const ButtonsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const PrimaryButton = styled.button`
  padding: 12px 0;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  width: 100%;
  cursor: pointer;
  background-color: #0066cc;
  color: white;
  border: none;

  &:hover {
    background-color: #0055b3;
  }
`;

const SecondaryButton = styled.button`
  padding: 12px 0;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  width: 100%;
  cursor: pointer;
  background-color: white;
  color: #333;
  border: 2px solid #d6dce1;

  &:hover {
    background-color: #f5f5f5;
  }
`;

interface Product {
  MaterialId: string;
  MaterialName: string;
  MaterialCode?: string;
  RetailPrice?: number;
  WholesalePrice?: number;
  SuppliersCount?: number;
  Unit?: string;
  image?: string;
}

interface ProductDetailClientProps {
  product: Product;
}

const ProductDetailClient: React.FC<ProductDetailClientProps> = ({
  product,
}) => {
  const router = useRouter();
  const { addToCart } = useCart();
  const [activeImageIndex, setActiveImageIndex] = useState(0);

  const handleBack = () => {
    router.back();
  };

  const handleAddToCart = () => {
    addToCart({
      id: product.MaterialId,
      MaterialName: product.MaterialName,
      MaterialCode: product.MaterialCode,
      retailPrice: product.RetailPrice,
      quantity: 1,
      image: product.image,
    });
  };

  const formatPrice = (price: number | undefined) => {
    if (!price) return "По запросу";
    return new Intl.NumberFormat("ru-RU").format(price) + " ₸";
  };

  // Заглушки для изображений
  const productImages = [
    {
      full: product.image || "/images/placeholder.png",
      alt: product.MaterialName,
    },
  ];

  return (
    <Layout>
      <ProductDetailContainer>
        <Breadcrumbs>
          <BackButton onClick={handleBack}>
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10 12L6 8L10 4"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Назад
          </BackButton>
          <a href="/">Прайс листы</a>
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6 12L10 8L6 4"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span>{product.MaterialName}</span>
        </Breadcrumbs>

        <Title>{product.MaterialName}</Title>

        <ProductContent>
          <ProductImages>
            <ThumbnailsColumn>
              {productImages.map((image, index) => (
                <Thumbnail
                  key={index}
                  $active={index === activeImageIndex}
                  onClick={() => setActiveImageIndex(index)}
                >
                  <img src={image.full} alt={image.alt} />
                </Thumbnail>
              ))}
            </ThumbnailsColumn>
            <MainImage>
              <img
                src={productImages[activeImageIndex].full}
                alt={productImages[activeImageIndex].alt}
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.onerror = null;
                  target.src = "/images/placeholder.png";
                }}
              />
            </MainImage>
          </ProductImages>

          <ProductInfo>
            <ProductInfoTitle>{product.MaterialName}</ProductInfoTitle>

            <PriceInfo>
              <PriceRow>
                <span>Сред. розн. цена:</span>
                <Price $bold>{formatPrice(product.RetailPrice)}</Price>
              </PriceRow>
              <PriceRow>
                <span>Сред. опт. цена:</span>
                <Price>{formatPrice(product.WholesalePrice)}</Price>
              </PriceRow>
              <PriceRow>
                <span>Поставщиков</span>
                <Price $bold>{product.SuppliersCount || 0}</Price>
              </PriceRow>
            </PriceInfo>

            <ButtonsContainer>
              <PrimaryButton onClick={handleAddToCart}>
                Выбрать поставщика
              </PrimaryButton>
              <SecondaryButton>Стать поставщиком</SecondaryButton>
            </ButtonsContainer>
          </ProductInfo>
        </ProductContent>
      </ProductDetailContainer>
    </Layout>
  );
};

export default ProductDetailClient;
