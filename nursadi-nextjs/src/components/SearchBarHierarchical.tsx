"use client";

import React, { useState, useEffect } from "react";
import styled from "styled-components";

const SearchContainer = styled.div`
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
`;
SearchContainer.displayName = "SearchContainer";

const SearchInputContainer = styled.div`
  position: relative;
  margin-bottom: 16px;
`;
SearchInputContainer.displayName = "SearchInputContainer";

const SearchInput = styled.input`
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #0066cc;
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.1);
  }

  &::placeholder {
    color: #999;
  }
`;
SearchInput.displayName = "SearchInput";

const SearchIcon = styled.div`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  pointer-events: none;

  svg {
    width: 20px;
    height: 20px;
  }
`;
SearchIcon.displayName = "SearchIcon";

const FilterSection = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;
FilterSection.displayName = "FilterSection";

const FilterSelect = styled.select`
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #0066cc;
  }

  &:disabled {
    background: #f5f5f5;
    color: #999;
    cursor: not-allowed;
  }
`;
FilterSelect.displayName = "FilterSelect";

const ClearButton = styled.button`
  padding: 8px 16px;
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: #e5e5e5;
    border-color: #999;
  }
`;
ClearButton.displayName = "ClearButton";

const ButtonGroup = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 16px;
  justify-content: flex-end;

  @media (max-width: 640px) {
    justify-content: stretch;

    button {
      flex: 1;
    }
  }
`;
ButtonGroup.displayName = "ButtonGroup";

interface SearchBarHierarchicalProps {
  onSearch: (query: string) => void;
  onCategoryChange: (category: any) => void;
  searchQuery: string;
  selectedCategory: any;
}

const SearchBarHierarchical: React.FC<SearchBarHierarchicalProps> = ({
  onSearch,
  onCategoryChange,
  searchQuery,
  selectedCategory,
}) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [department, setDepartment] = useState("");
  const [section, setSection] = useState("");
  const [subsection, setSubsection] = useState("");
  const [group, setGroup] = useState("");

  // Синхронизируем локальное состояние с пропсами
  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  useEffect(() => {
    if (selectedCategory) {
      setDepartment(selectedCategory.department || "");
      setSection(selectedCategory.section || "");
      setSubsection(selectedCategory.subsection || "");
      setGroup(selectedCategory.group || "");
    }
  }, [selectedCategory]);

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocalSearchQuery(value);

    // Вызываем поиск с небольшой задержкой
    const timeoutId = setTimeout(() => {
      onSearch(value);
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(localSearchQuery);
  };

  const handleCategoryChange = (field: string, value: string) => {
    let newCategory = { ...selectedCategory };

    switch (field) {
      case "department":
        setDepartment(value);
        setSection("");
        setSubsection("");
        setGroup("");
        newCategory = {
          department: value,
          section: "",
          subsection: "",
          group: "",
        };
        break;
      case "section":
        setSection(value);
        setSubsection("");
        setGroup("");
        newCategory = {
          ...newCategory,
          section: value,
          subsection: "",
          group: "",
        };
        break;
      case "subsection":
        setSubsection(value);
        setGroup("");
        newCategory = { ...newCategory, subsection: value, group: "" };
        break;
      case "group":
        setGroup(value);
        newCategory = { ...newCategory, group: value };
        break;
    }

    onCategoryChange(newCategory);
  };

  const handleClear = () => {
    setLocalSearchQuery("");
    setDepartment("");
    setSection("");
    setSubsection("");
    setGroup("");
    onSearch("");
    onCategoryChange(null);
  };

  return (
    <SearchContainer>
      <form onSubmit={handleSearchSubmit}>
        <SearchInputContainer>
          <SearchIcon>
            <svg
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </SearchIcon>
          <SearchInput
            type="text"
            placeholder="Введите название товара..."
            value={localSearchQuery}
            onChange={handleSearchInputChange}
          />
        </SearchInputContainer>
      </form>

      <FilterSection>
        <FilterSelect
          value={department}
          onChange={(e) => handleCategoryChange("department", e.target.value)}
        >
          <option value="">Выберите отдел</option>
          <option value="building">Строительные материалы</option>
          <option value="tools">Инструменты</option>
          <option value="equipment">Оборудование</option>
        </FilterSelect>

        <FilterSelect
          value={section}
          onChange={(e) => handleCategoryChange("section", e.target.value)}
          disabled={!department}
        >
          <option value="">Выберите раздел</option>
          {department === "building" && (
            <>
              <option value="cement">Цемент и бетон</option>
              <option value="brick">Кирпич и блоки</option>
              <option value="wood">Пиломатериалы</option>
            </>
          )}
          {department === "tools" && (
            <>
              <option value="hand">Ручные инструменты</option>
              <option value="power">Электроинструменты</option>
              <option value="measuring">Измерительные инструменты</option>
            </>
          )}
        </FilterSelect>

        <FilterSelect
          value={subsection}
          onChange={(e) => handleCategoryChange("subsection", e.target.value)}
          disabled={!section}
        >
          <option value="">Выберите подраздел</option>
          {/* Здесь можно добавить логику для подразделов */}
        </FilterSelect>

        <FilterSelect
          value={group}
          onChange={(e) => handleCategoryChange("group", e.target.value)}
          disabled={!subsection}
        >
          <option value="">Выберите группу</option>
          {/* Здесь можно добавить логику для групп */}
        </FilterSelect>
      </FilterSection>

      <ButtonGroup>
        <ClearButton type="button" onClick={handleClear}>
          Очистить фильтры
        </ClearButton>
      </ButtonGroup>
    </SearchContainer>
  );
};

SearchBarHierarchical.displayName = "SearchBarHierarchical";

export default SearchBarHierarchical;
