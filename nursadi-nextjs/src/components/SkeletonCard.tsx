"use client";

import React from "react";
import styled, { keyframes } from "styled-components";

const shimmer = keyframes`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
`;

const SkeletonContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  height: 100%;
`;
SkeletonContainer.displayName = "SkeletonContainer";

const SkeletonElement = styled.div`
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: ${shimmer} 1.5s infinite;
  border-radius: 4px;
`;
SkeletonElement.displayName = "SkeletonElement";

const SkeletonImage = styled(SkeletonElement)`
  height: 160px;
  margin-bottom: 16px;
`;
SkeletonImage.displayName = "SkeletonImage";

const SkeletonTitle = styled(SkeletonElement)`
  height: 20px;
  margin-bottom: 8px;
  width: 80%;
`;
SkeletonTitle.displayName = "SkeletonTitle";

const SkeletonSubtitle = styled(SkeletonElement)`
  height: 16px;
  margin-bottom: 8px;
  width: 60%;
`;
SkeletonSubtitle.displayName = "SkeletonSubtitle";

const SkeletonPrice = styled(SkeletonElement)`
  height: 18px;
  margin: 12px 0;
  width: 40%;
`;
SkeletonPrice.displayName = "SkeletonPrice";

const SkeletonButtonGroup = styled.div`
  display: flex;
  gap: 8px;
  margin-top: auto;
`;
SkeletonButtonGroup.displayName = "SkeletonButtonGroup";

const SkeletonButton = styled(SkeletonElement)`
  height: 36px;
  flex: 1;
`;
SkeletonButton.displayName = "SkeletonButton";

const SkeletonButtonSmall = styled(SkeletonElement)`
  height: 36px;
  width: 80px;
`;
SkeletonButtonSmall.displayName = "SkeletonButtonSmall";

const SkeletonCard: React.FC = () => {
  return (
    <SkeletonContainer>
      <SkeletonImage />
      <SkeletonTitle />
      <SkeletonSubtitle />
      <SkeletonPrice />
      <SkeletonButtonGroup>
        <SkeletonButton />
        <SkeletonButtonSmall />
      </SkeletonButtonGroup>
    </SkeletonContainer>
  );
};

SkeletonCard.displayName = "SkeletonCard";

export default SkeletonCard;
