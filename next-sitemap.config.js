/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.SITE_URL || 'https://sadi.kz',
  generateRobotsTxt: true,
  generateIndexSitemap: false,
  exclude: [
    '/auth',
    '/cart',
    '/create-tender',
    '/tender-form',
    '/api/*',
    '/admin/*',
  ],
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/auth', '/cart', '/admin', '/api'],
      },
    ],
    additionalSitemaps: [
      'https://sadi.kz/sitemap.xml',
    ],
  },
  transform: async (config, path) => {
    // Настройка приоритетов и частоты обновления для разных типов страниц
    let priority = 0.7;
    let changefreq = 'weekly';

    if (path === '/') {
      priority = 1.0;
      changefreq = 'daily';
    } else if (path.startsWith('/products/page/')) {
      priority = 0.9;
      changefreq = 'daily';
    } else if (path.startsWith('/products/')) {
      priority = 0.8;
      changefreq = 'weekly';
    } else if (path.startsWith('/category/')) {
      priority = 0.8;
      changefreq = 'weekly';
    } else if (path.startsWith('/search/')) {
      priority = 0.6;
      changefreq = 'monthly';
    }

    return {
      loc: path,
      changefreq,
      priority,
      lastmod: new Date().toISOString(),
    };
  },
  additionalPaths: async (config) => {
    const paths = [];

    try {
      // Добавляем популярные категории
      const categories = [
        'cement',
        'armatura', 
        'kirpich',
        'plitka',
        'instrumenty',
        'oborudovanie',
        'krovlya',
        'uteplitel',
        'gipsokarton',
        'dveri',
      ];

      categories.forEach(category => {
        paths.push({
          loc: `/category/${category}`,
          changefreq: 'weekly',
          priority: 0.8,
          lastmod: new Date().toISOString(),
        });
      });

      // Добавляем популярные поисковые запросы
      const searches = [
        'цемент',
        'арматура',
        'кирпич',
        'плитка',
        'инструменты',
        'краска',
        'утеплитель',
        'гипсокартон',
        'двери',
        'окна',
      ];

      searches.forEach(query => {
        paths.push({
          loc: `/search/${encodeURIComponent(query)}`,
          changefreq: 'monthly',
          priority: 0.6,
          lastmod: new Date().toISOString(),
        });
      });

      // Добавляем страницы каталога (первые 50 страниц)
      for (let page = 1; page <= 50; page++) {
        paths.push({
          loc: `/products/page/${page}`,
          changefreq: 'daily',
          priority: page === 1 ? 0.9 : 0.8,
          lastmod: new Date().toISOString(),
        });
      }

    } catch (error) {
      console.error('Ошибка при генерации дополнительных путей для sitemap:', error);
    }

    return paths;
  },
};
